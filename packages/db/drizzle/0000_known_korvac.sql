CREATE TYPE "public"."actor_type" AS ENUM('USER', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'SYSTEM');--> statement-breakpoint
CREATE TYPE "public"."billing_method" AS ENUM('CREDIT_CARD', 'PAYPAL', 'BAN<PERSON>_TRANSFER', 'CRYPTO');--> statement-breakpoint
CREATE TYPE "public"."billing_profile_status" AS ENUM('ACTIVE', 'INACTIVE', 'DEPRECATED');--> statement-breakpoint
CREATE TYPE "public"."bot_status" AS ENUM('RUNNING', 'STOPPED', 'ERROR');--> statement-breakpoint
CREATE TYPE "public"."call_direction" AS ENUM('INBOUND', 'OUTBOUND');--> statement-breakpoint
CREATE TYPE "public"."call_source" AS ENUM('PBX_INCOMING', 'PBX_OUTGOING', 'MANUAL', 'CHATBOT_TELEGRAM', 'CHATBOT_VIBER', 'CHATBOT_WHATSAPP', 'SCHEDULED_ACTIVATION', 'APP_PASSENGER', 'APP_DRIVER', 'API', 'DUPLICATE');--> statement-breakpoint
CREATE TYPE "public"."call_status" AS ENUM('SCHEDULED', 'RINGING', 'ANSWERED', 'MISSED', 'ENDED', 'FAILED');--> statement-breakpoint
CREATE TYPE "public"."event_type" AS ENUM('MESSAGE_RECEIVED', 'MESSAGE_SENT', 'USER_LINKED', 'USER_UNLINKED', 'CONSENT_GRANTED', 'CONSENT_REVOKED', 'VERIFICATION_REQUESTED', 'VERIFICATION_SUCCEEDED', 'VERIFICATION_FAILED', 'SESSION_STARTED', 'SESSION_ENDED', 'ERROR', 'WEBHOOK_RECEIVED');--> statement-breakpoint
CREATE TYPE "public"."fuel_type" AS ENUM('GASOLINE', 'DIESEL', 'ELECTRIC', 'HYBRID', 'LPG', 'CNG', 'HYDROGEN', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."group_status" AS ENUM('ACTIVE', 'PENDING', 'INACTIVE');--> statement-breakpoint
CREATE TYPE "public"."group_type" AS ENUM('FRANCHISE', 'AGGREGATOR', 'BRAND', 'CORPORATE');--> statement-breakpoint
CREATE TYPE "public"."invoice_status" AS ENUM('PENDING', 'PAID', 'OPEN', 'FAILED');--> statement-breakpoint
CREATE TYPE "public"."kyc_status" AS ENUM('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED');--> statement-breakpoint
CREATE TYPE "public"."message_direction" AS ENUM('IN', 'OUT');--> statement-breakpoint
CREATE TYPE "public"."message_type" AS ENUM('TEXT', 'IMAGE', 'VOICE', 'LOCATION', 'VERIFICATION', 'DOCUMENT', 'STICKER', 'BUTTON', 'TEMPLATE', 'INTERACTIVE', 'CAROUSEL', 'GROUP_CHAT', 'UNKNOWN');--> statement-breakpoint
CREATE TYPE "public"."onboarding_status" AS ENUM('INCOMPLETE', 'IN_PROGRESS', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."operator_status" AS ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING');--> statement-breakpoint
CREATE TYPE "public"."payment_method" AS ENUM('CARD', 'WIRE', 'CRYPTO', 'INVOICE');--> statement-breakpoint
CREATE TYPE "public"."payment_status" AS ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'REFUNDED', 'CANCELLED');--> statement-breakpoint
CREATE TYPE "public"."pbx_tenant_association_type" AS ENUM('SINGLE_TENANT', 'MULTI_TENANT_DID', 'MULTI_TENANT_CHANNEL_VAR', 'MULTI_TENANT_QUEUE_NAME', 'MULTI_TENANT_EXTENSION_PREFIX', 'MULTI_TENANT_CHANNEL_NAME_PREFIX', 'MULTI_TENANT_CALLER_ID_PREFIX');--> statement-breakpoint
CREATE TYPE "public"."pbx_type" AS ENUM('ASTERISK_ARI', 'FREEPBX_GRAPHQL');--> statement-breakpoint
CREATE TYPE "public"."profile_change_type" AS ENUM('ONBOARDING', 'PHONE_CHANGE', 'ADMIN_UPDATE');--> statement-breakpoint
CREATE TYPE "public"."promo_status" AS ENUM('ACTIVE', 'INACTIVE', 'EXPIRED', 'LIMIT_REACHED');--> statement-breakpoint
CREATE TYPE "public"."promotion_type" AS ENUM('PERCENTAGE', 'VALUE', 'FREE_RIDE');--> statement-breakpoint
CREATE TYPE "public"."provider" AS ENUM('TELEGRAM', 'VIBER', 'FACEBOOK', 'GOOGLE', 'APPLE', 'PHONE');--> statement-breakpoint
CREATE TYPE "public"."reminder_urgency" AS ENUM('NOT_URGENT', 'URGENT', 'VERY_URGENT', 'PAST_DUE');--> statement-breakpoint
CREATE TYPE "public"."ride_rating" AS ENUM('ONE', 'TWO', 'THREE', 'FOUR', 'FIVE');--> statement-breakpoint
CREATE TYPE "public"."ride_status" AS ENUM('SEARCHING', 'ASSIGNED', 'PICKED_UP', 'COMPLETED', 'CANCELLED');--> statement-breakpoint
CREATE TYPE "public"."ride_type" AS ENUM('RIDE', 'DELIVERY', 'POOLED', 'SHARED');--> statement-breakpoint
CREATE TYPE "public"."scheduled_call_status" AS ENUM('PENDING', 'ACTIVE_ONCE', 'ACTIVE_RECURRING', 'COMPLETED', 'PAUSED', 'DISABLED');--> statement-breakpoint
CREATE TYPE "public"."setting_type" AS ENUM('SYSTEM', 'PAYMENT', 'I18N', 'UI');--> statement-breakpoint
CREATE TYPE "public"."sms_log_direction" AS ENUM('OUTBOUND_OPERATOR', 'INBOUND_REPLY');--> statement-breakpoint
CREATE TYPE "public"."sms_status" AS ENUM('PENDING', 'QUEUED', 'SENT', 'FAILED', 'DELIVERED', 'UNDELIVERED', 'RECEIVED', 'READ');--> statement-breakpoint
CREATE TYPE "public"."source" AS ENUM('TELEGRAM', 'ANDROID', 'MQTT', 'WEB', 'MANUAL');--> statement-breakpoint
CREATE TYPE "public"."support_ticket_status" AS ENUM('OPEN', 'CLOSED', 'PENDING');--> statement-breakpoint
CREATE TYPE "public"."tenant_plan" AS ENUM('FREE', 'PRO', 'ENTERPRISE');--> statement-breakpoint
CREATE TYPE "public"."tenant_status" AS ENUM('ACTIVE', 'SUSPENDED', 'CANCELLED');--> statement-breakpoint
CREATE TYPE "public"."user_role" AS ENUM('PASSENGER', 'DRIVER', 'OPERATOR', 'MANAGER', 'ADMIN', 'SYSTEM_SUPPORT');--> statement-breakpoint
CREATE TYPE "public"."user_status" AS ENUM('ACTIVE', 'INACTIVE', 'BANNED', 'SUSPENDED');--> statement-breakpoint
CREATE TYPE "public"."vehicle_expenses_type" AS ENUM('TAX', 'INSURANCE', 'REGISTRATION', 'MAINTENANCE', 'FUEL', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."vehicle_status" AS ENUM('ACTIVE', 'DISABLED', 'DECOMMISSIONED', 'REPAIR');--> statement-breakpoint
CREATE TYPE "public"."verification_method" AS ENUM('SMS', 'EMAIL', 'PHONE_CALL', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."verification_status" AS ENUM('INITIATED', 'CODE_SENT', 'VERIFIED', 'FAILED', 'EXPIRED');--> statement-breakpoint
CREATE TABLE "addresses" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid,
	"street_line_1" varchar(255),
	"street_line_2" varchar(255),
	"city" varchar(100),
	"state_province" varchar(100),
	"postal_code" varchar(20),
	"country_code" varchar(2) NOT NULL,
	"formatted_address" text,
	"raw_input_address" text,
	"geom" geometry(point),
	"latitude" double precision,
	"longitude" double precision,
	"geocoding_provider" varchar(50),
	"geocoding_accuracy" varchar(50),
	"osm_place_id" varchar(50),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "areas" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"name" varchar(255) NOT NULL,
	"geom" geometry(point) NOT NULL,
	"city" varchar(255),
	"country" varchar(2),
	"osm_tags" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "audit_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"actor_id" uuid,
	"actor_type" "actor_type" NOT NULL,
	"tenant_id" uuid,
	"event_type" varchar(255) NOT NULL,
	"target_table" varchar(255),
	"target_id" uuid,
	"description" text,
	"details" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "bots" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"token" text NOT NULL,
	"webhook_path_segment" varchar(100) NOT NULL,
	"bot_username" varchar(255),
	"is_enabled" boolean DEFAULT true NOT NULL,
	"config" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "bots_webhook_path_segment_unique" UNIQUE("webhook_path_segment")
);
--> statement-breakpoint
CREATE TABLE "chat" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"type" varchar(50) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_config" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"provider_id" uuid NOT NULL,
	"settings" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_events" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatbot_instance_id" uuid NOT NULL,
	"event_type" "event_type" NOT NULL,
	"user_id" uuid,
	"session_id" uuid,
	"message_id" uuid,
	"details" jsonb NOT NULL,
	"occurred_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "chatbot_instance" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"provider_id" uuid NOT NULL,
	"name" varchar(100) NOT NULL,
	"bot_username" varchar(100),
	"webhook_url" varchar(1024),
	"config" jsonb,
	"is_active" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "chatbot_message" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatbot_session_id" uuid NOT NULL,
	"chatbot_user_id" uuid NOT NULL,
	"direction" "message_direction" NOT NULL,
	"sent_at" timestamp NOT NULL,
	"message_type" "message_type" NOT NULL,
	"content" text NOT NULL,
	"phone_verified" boolean DEFAULT false NOT NULL,
	"verification_reference" varchar(255),
	"replied_to_id" uuid,
	"error_flag" boolean DEFAULT false NOT NULL,
	"provider_message_id" varchar(255),
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "chatbot_message_with_geolocation" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"message_id" uuid NOT NULL,
	"location" geometry(point) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_provider" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"name" varchar(50) NOT NULL,
	"description" varchar(500),
	"logo_url" varchar(1024),
	"features" jsonb,
	"enabled" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_session" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatbot_user_id" uuid NOT NULL,
	"tenant_id" uuid NOT NULL,
	"started_at" timestamp NOT NULL,
	"ended_at" timestamp,
	"verified" boolean DEFAULT false NOT NULL,
	"geolocation" geometry(point),
	"accuracy" double precision,
	"context" varchar(100),
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "chatbot_users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"chatbot_instance_id" uuid NOT NULL,
	"provider_user_id" varchar(255),
	"phone_verified" boolean DEFAULT false NOT NULL,
	"verification_date" timestamp NOT NULL,
	"consent" boolean DEFAULT false NOT NULL,
	"consent_date" timestamp,
	"consent_revoked_at" timestamp,
	"blocked" boolean DEFAULT false NOT NULL,
	"verified_at" timestamp,
	"locale" varchar(10),
	"joined_at" timestamp DEFAULT now() NOT NULL,
	"last_seen_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "chatbot" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"provider" "provider" NOT NULL,
	"bot_id" varchar(255) NOT NULL,
	"config" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "dispatch_assignment" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"operator_id" uuid NOT NULL,
	"ride_id" uuid NOT NULL,
	"assigned_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "driver_vehicle" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"driver_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"from_time" timestamp NOT NULL,
	"to_time" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "geodata_entries" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid,
	"address_id" uuid,
	"country" varchar(2),
	"city" varchar(255),
	"address" varchar(255),
	"geom" geometry(point) NOT NULL,
	"source" "source" NOT NULL,
	"user_id" uuid,
	"accuracy" double precision,
	"area_id" uuid,
	"osm_tags" jsonb,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "i18n_translations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid,
	"namespace" varchar(100),
	"key" varchar(255) NOT NULL,
	"value" text NOT NULL,
	"locale" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "invoices" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"currency" varchar(3) NOT NULL,
	"status" "invoice_status" DEFAULT 'PENDING' NOT NULL,
	"due_date" timestamp NOT NULL,
	"issued_at" timestamp DEFAULT now() NOT NULL,
	"paid_at" timestamp,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "map_provider" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"provider_name" varchar(50) NOT NULL,
	"access_token" varchar(255) NOT NULL,
	"refresh_token" varchar(255),
	"expires_at" timestamp,
	"is_active" boolean DEFAULT true NOT NULL,
	"config" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "messages" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"chat_id" uuid NOT NULL,
	"from_user_id" uuid NOT NULL,
	"to_user_id" uuid,
	"provider_message_id" varchar(255),
	"direction" "message_direction" NOT NULL,
	"via_channel" "provider" NOT NULL,
	"message_type" "message_type" NOT NULL,
	"content" text NOT NULL,
	"phone_verified" boolean,
	"verification_reference" varchar(255),
	"geolocation" geometry(point),
	"accuracy" double precision,
	"sms_status" "sms_status",
	"related_pbx_call_id" uuid,
	"replied_to_message_id" uuid,
	"error_flag" boolean DEFAULT false,
	"metadata" jsonb,
	"sent_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "multi_tenant_group" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"type" "group_type" NOT NULL,
	"contact_email" varchar(255),
	"contact_phone" varchar(50),
	"parent_group_id" uuid,
	"settings" jsonb,
	"status" "group_status" DEFAULT 'PENDING' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "operator_extensions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"user_tenant_id" uuid NOT NULL,
	"status" "operator_status" DEFAULT 'ACTIVE',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "operator_shift" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"operator_id" uuid NOT NULL,
	"shift_start" timestamp NOT NULL,
	"shift_end" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "operator_performance_stats" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"operator_user_id" uuid NOT NULL,
	"date" timestamp with time zone NOT NULL,
	"shift_id" uuid,
	"answered_calls" integer DEFAULT 0 NOT NULL,
	"missed_calls" integer DEFAULT 0 NOT NULL,
	"outbound_calls" integer DEFAULT 0 NOT NULL,
	"total_call_duration_seconds" integer DEFAULT 0 NOT NULL,
	"avg_handle_time_seconds" integer DEFAULT 0 NOT NULL,
	"rides_dispatched" integer DEFAULT 0 NOT NULL,
	"sms_sent" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "operators" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"user_tenant_id" uuid NOT NULL,
	"first_name" varchar(100) NOT NULL,
	"last_name" varchar(100) NOT NULL,
	"phone" varchar(20) NOT NULL,
	"status" "operator_status" DEFAULT 'ACTIVE' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "payment" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"ride_id" uuid,
	"user_id" uuid NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"currency" varchar(3) NOT NULL,
	"method" "payment_method" NOT NULL,
	"processor_ref" varchar(255),
	"status" "payment_status" DEFAULT 'PENDING' NOT NULL,
	"processed_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "pbx_call_additional_operators" (
	"pbx_call_id" uuid NOT NULL,
	"operator_id" uuid NOT NULL,
	"assigned_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "pbx_call_additional_operators_pbx_call_id_operator_id_pk" PRIMARY KEY("pbx_call_id","operator_id")
);
--> statement-breakpoint
CREATE TABLE "pbx_call" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"user_id" uuid,
	"operator_id" uuid,
	"ride_id" uuid,
	"direction" "call_direction" NOT NULL,
	"status" "call_status" NOT NULL,
	"duration" integer,
	"recording_url" varchar(1024),
	"started_at" timestamp with time zone NOT NULL,
	"answered_at" timestamp with time zone,
	"ended_at" timestamp with time zone,
	"assigned_at" timestamp with time zone,
	"external_pbx_id" varchar(255),
	"from_phone_number" varchar(50),
	"to_phone_number" varchar(50),
	"caller_name" varchar(255),
	"numeric_extension" integer,
	"termination_reason" varchar(100),
	"disposition_code" varchar(100),
	"pbx_raw_details" jsonb,
	"source" "call_source",
	"customer_phone_number" varchar(50),
	"location_text" text,
	"destination_text" text,
	"area_id" uuid,
	"area_name" varchar(255),
	"eta_minutes" integer,
	"gis_data" jsonb,
	"vehicle_identifiers" json,
	"comment" text,
	"is_locked" boolean DEFAULT false,
	"language_code" varchar(10),
	"call_metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "pbx_instances" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"type" "pbx_type" NOT NULL,
	"is_enabled" boolean DEFAULT true NOT NULL,
	"connection_details" jsonb NOT NULL,
	"tenant_association_type" "pbx_tenant_association_type" NOT NULL,
	"default_tenant_id" uuid,
	"tenant_routing_rules" jsonb DEFAULT 'null'::jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "pbx_instances_name_unique" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "promos" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"code" varchar(20) NOT NULL,
	"description" varchar(255),
	"discount_value" integer NOT NULL,
	"discount_type" "promotion_type" NOT NULL,
	"max_uses" integer,
	"uses" integer DEFAULT 0 NOT NULL,
	"start_date" timestamp NOT NULL,
	"end_date" timestamp NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"status" "promo_status" DEFAULT 'ACTIVE' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "promotion" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"code" varchar(255) NOT NULL,
	"type" "promotion_type" NOT NULL,
	"value" numeric(10, 2) NOT NULL,
	"usage_limit" integer,
	"valid_from" timestamp,
	"valid_to" timestamp,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ride_event" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"ride_id" uuid NOT NULL,
	"event_type" varchar(50) NOT NULL,
	"details" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ride_order" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"passenger_id" uuid,
	"driver_id" uuid,
	"vehicle_id" uuid,
	"pickup_address" varchar(255),
	"pickup_latlng" jsonb,
	"dropoff_address" varchar(255),
	"dropoff_latlng" jsonb,
	"scheduled_time" timestamp,
	"confirmed_time" timestamp,
	"start_time" timestamp,
	"end_time" timestamp,
	"status" "ride_status" DEFAULT 'SEARCHING' NOT NULL,
	"order_type" "ride_type" DEFAULT 'RIDE' NOT NULL,
	"estimated_fare" numeric(10, 2),
	"currency" varchar(3),
	"payment_method" varchar(50),
	"paid" boolean DEFAULT false,
	"promo_id" uuid,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ride_rating" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"ride_id" uuid NOT NULL,
	"from_user_id" uuid NOT NULL,
	"to_user_id" uuid NOT NULL,
	"rating" integer NOT NULL,
	"feedback" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "rides" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"passenger_id" uuid NOT NULL,
	"driver_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"status" "ride_status" DEFAULT 'SEARCHING' NOT NULL,
	"order_type" "ride_type" DEFAULT 'RIDE' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "roles" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"name" varchar(50) NOT NULL,
	"description" varchar(1024),
	"permissions" jsonb,
	"is_default" boolean DEFAULT false NOT NULL,
	"is_system" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "scheduled_calls" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"created_by_operator_id" uuid,
	"customer_user_id" uuid,
	"phone_number" varchar(50),
	"base_call_details" jsonb,
	"scheduled_timestamp" timestamp with time zone,
	"arrival_time_offset_minutes" integer,
	"repeat_pattern" jsonb,
	"next_activation_timestamp" timestamp with time zone,
	"last_activated_timestamp" timestamp with time zone,
	"is_active" boolean DEFAULT true NOT NULL,
	"status" "scheduled_call_status" DEFAULT 'PENDING' NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sms_log" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"direction" "sms_log_direction" NOT NULL,
	"from_number" varchar(50) NOT NULL,
	"to_number" varchar(50) NOT NULL,
	"content" text NOT NULL,
	"status" "sms_status",
	"gateway_reference_id" varchar(255),
	"operator_user_id" uuid,
	"related_pbx_call_id" uuid,
	"logged_at" timestamp with time zone DEFAULT now() NOT NULL,
	"error_message" text
);
--> statement-breakpoint
CREATE TABLE "support_ticket" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"subject" varchar(256) NOT NULL,
	"details" text NOT NULL,
	"status" "support_ticket_status" NOT NULL,
	"assigned_to_id" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"closed_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "system_settings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid,
	"key" varchar(255) NOT NULL,
	"value" jsonb NOT NULL,
	"description" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "telegram_chats" (
	"id" bigint PRIMARY KEY NOT NULL,
	"type" varchar(32) NOT NULL,
	"title" varchar(256),
	"username" varchar(32),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"last_seen" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "telegram_messages" (
	"id" bigint PRIMARY KEY NOT NULL,
	"chat_id" bigint NOT NULL,
	"user_id" integer,
	"message_type" varchar(32),
	"text" varchar(4096),
	"file_id" varchar(256),
	"caption" varchar(1024),
	"reply_to_message_id" bigint,
	"forward_from_user_id" integer,
	"forward_from_chat_id" bigint,
	"edit_date" timestamp with time zone,
	"entities" json,
	"command" varchar(64),
	"payload" json,
	"service_type" varchar(32),
	"date" timestamp with time zone NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"raw_data" json
);
--> statement-breakpoint
CREATE TABLE "telegram_users" (
	"id" bigint PRIMARY KEY NOT NULL,
	"first_name" varchar(255),
	"last_name" varchar(255),
	"username" varchar(255)
);
--> statement-breakpoint
CREATE TABLE "telegram_chat_members" (
	"chat_id" bigint NOT NULL,
	"user_id" integer NOT NULL,
	CONSTRAINT "telegram_chat_members_chat_id_user_id_pk" PRIMARY KEY("chat_id","user_id")
);
--> statement-breakpoint
CREATE TABLE "telegram_user_settings" (
	"user_id" integer PRIMARY KEY NOT NULL,
	"language" varchar(8),
	"notifications" boolean DEFAULT true
);
--> statement-breakpoint
CREATE TABLE "tenant_billing_profile" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"name" varchar(255) NOT NULL,
	"billing_email" varchar(255),
	"billing_phone" varchar(50),
	"tax_id" varchar(100),
	"address" varchar(512),
	"country" varchar(100),
	"currency" varchar(10),
	"payment_method" "payment_method" NOT NULL,
	"default_profile" boolean DEFAULT false NOT NULL,
	"status" "billing_profile_status" DEFAULT 'ACTIVE' NOT NULL,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tenant_bots" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"bot_name" varchar(100),
	"telegram_bot_token" varchar(255) NOT NULL,
	"webhook_domain" varchar(255),
	"webhook_path" varchar(255),
	"webhook_secret_token" varchar(255),
	"status" "bot_status" DEFAULT 'STOPPED' NOT NULL,
	"last_status_update" timestamp with time zone DEFAULT now() NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tenant_localization" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"default_locale" varchar(10) NOT NULL,
	"supported_locales" jsonb NOT NULL,
	"timezone" varchar(64) NOT NULL,
	"currency_symbol" varchar(10) NOT NULL,
	"labels" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "tenant_settings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"key" varchar(255) NOT NULL,
	"value" jsonb,
	"type" "setting_type" NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tenants" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"legal_name" varchar(255),
	"email" varchar(255),
	"phone" varchar(50),
	"logo_url" varchar(1024),
	"address" varchar(512),
	"country" varchar(2),
	"timezone" varchar(64),
	"plan" "tenant_plan",
	"status" "tenant_status" DEFAULT 'ACTIVE' NOT NULL,
	"multi_tenant_group_id" uuid,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_bot_roles" (
	"telegram_user_id" bigint NOT NULL,
	"bot_id" uuid NOT NULL,
	"role_id" uuid NOT NULL,
	CONSTRAINT "pk_user_bot_role" PRIMARY KEY("telegram_user_id","bot_id","role_id")
);
--> statement-breakpoint
CREATE TABLE "user_consent" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"tenant_id" uuid,
	"consent_type" varchar(50) NOT NULL,
	"granted" boolean NOT NULL,
	"granted_at" timestamp DEFAULT now() NOT NULL,
	"revoked_at" timestamp,
	"deleted_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "user_identity" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"provider" "user_role" NOT NULL,
	"external_id" varchar(255) NOT NULL,
	"display" varchar(255) NOT NULL,
	"avatar_url" varchar(1024),
	"metadata" json DEFAULT '{}'::json NOT NULL,
	"linked_at" timestamp DEFAULT now() NOT NULL,
	"unlinked_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_kyc" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid,
	"user_id" uuid NOT NULL,
	"provider" varchar(255) NOT NULL,
	"submitted_at" timestamp DEFAULT now() NOT NULL,
	"status" "kyc_status" NOT NULL,
	"document_type" varchar(255) NOT NULL,
	"document_number" varchar(255) NOT NULL,
	"country" varchar(100) NOT NULL,
	"expiry_date" date,
	"rejection_reason" varchar(1024),
	"metadata" json
);
--> statement-breakpoint
CREATE TABLE "user_onboarding" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"tenant_id" uuid,
	"completed_steps" jsonb,
	"status" "onboarding_status" NOT NULL,
	"completed_at" timestamp,
	"metadata" jsonb,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_profile_history" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"changed_by_id" uuid,
	"change_type" "profile_change_type" NOT NULL,
	"old_value" json NOT NULL,
	"new_value" json NOT NULL,
	"changed_at" timestamp DEFAULT now() NOT NULL,
	"context" varchar(50) NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_tenant" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"tenant_id" uuid NOT NULL,
	"role_id" uuid NOT NULL,
	"status" "user_status" DEFAULT 'ACTIVE' NOT NULL,
	"is_primary" boolean DEFAULT false NOT NULL,
	"last_used" timestamp,
	"registered_at" timestamp DEFAULT now() NOT NULL,
	"invited_by_id" uuid,
	"metadata" json DEFAULT '{}'::json NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"phone" varchar(20) NOT NULL,
	"email" varchar(255),
	"external_id" varchar(255),
	"verified" boolean DEFAULT false NOT NULL,
	"registered_at" timestamp DEFAULT now() NOT NULL,
	"last_login_at" timestamp,
	"legal_name" varchar(255),
	"display_name" varchar(255),
	"date_of_birth" timestamp,
	"language" varchar(10),
	"avatar_url" varchar(1024),
	"communication_opt_in" json,
	"privacy_flags" json,
	"current_location" geometry(point),
	"location_accuracy" double precision,
	"location_updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp,
	"metadata" json,
	CONSTRAINT "users_phone_unique" UNIQUE("phone"),
	CONSTRAINT "users_email_unique" UNIQUE("email"),
	CONSTRAINT "users_external_id_unique" UNIQUE("external_id")
);
--> statement-breakpoint
CREATE TABLE "vehicle" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"license_plate" varchar(20) NOT NULL,
	"make" varchar(100) NOT NULL,
	"model" varchar(100) NOT NULL,
	"color" varchar(50),
	"year" integer,
	"fuel_type" "fuel_type",
	"registration_id" varchar(100),
	"status" "vehicle_status" DEFAULT 'ACTIVE' NOT NULL,
	"purchase_date" date,
	"purchase_price" numeric(12, 2),
	"sold_date" date,
	"sold_price" numeric(12, 2),
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_expense" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"expense_type" "vehicle_expenses_type" NOT NULL,
	"date" date NOT NULL,
	"description" text NOT NULL,
	"cost" numeric(10, 2) NOT NULL,
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_note" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"description" varchar(255),
	"note" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_odometer_log" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"date" date NOT NULL,
	"initial_value" integer,
	"odometer_value" integer NOT NULL,
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_parts_log" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"date" date NOT NULL,
	"part_number" varchar(100),
	"supplier" varchar(255),
	"description" text NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"cost_per_unit" numeric(10, 2),
	"total_cost" numeric(12, 2),
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_reminder" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"urgency" "reminder_urgency" DEFAULT 'NOT_URGENT' NOT NULL,
	"trigger_odometer" integer,
	"trigger_date" date,
	"description" text NOT NULL,
	"notes" text,
	"is_dismissed" boolean DEFAULT false NOT NULL,
	"dismissed_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_repair" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"date" date NOT NULL,
	"odometer" integer,
	"description" text NOT NULL,
	"cost" numeric(10, 2),
	"notes" text,
	"repair_shop" varchar(255),
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_service_record" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"date" date NOT NULL,
	"odometer" integer,
	"description" text NOT NULL,
	"cost" numeric(10, 2),
	"notes" text,
	"service_provider" varchar(255),
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_upgrade" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"date" date NOT NULL,
	"odometer" integer,
	"description" text NOT NULL,
	"cost" numeric(10, 2),
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "verification_event" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatbot_user_id" uuid NOT NULL,
	"method" "verification_method" NOT NULL,
	"status" "verification_status" NOT NULL,
	"reference" varchar(255),
	"initiated_at" timestamp DEFAULT now() NOT NULL,
	"completed_at" timestamp,
	"expires_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "wallet" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"tenant_id" uuid NOT NULL,
	"currency" varchar(3) NOT NULL,
	"balance" numeric(10, 2) DEFAULT '0.0' NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "webhook_subscriber" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"url" varchar(255) NOT NULL,
	"events" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "addresses" ADD CONSTRAINT "addresses_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "areas" ADD CONSTRAINT "areas_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bots" ADD CONSTRAINT "bots_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chat" ADD CONSTRAINT "chat_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_config" ADD CONSTRAINT "chatbot_config_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_config" ADD CONSTRAINT "chatbot_config_provider_id_chatbot_provider_id_fk" FOREIGN KEY ("provider_id") REFERENCES "public"."chatbot_provider"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_events" ADD CONSTRAINT "chatbot_events_chatbot_instance_id_chatbot_instance_id_fk" FOREIGN KEY ("chatbot_instance_id") REFERENCES "public"."chatbot_instance"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_events" ADD CONSTRAINT "chatbot_events_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_events" ADD CONSTRAINT "chatbot_events_session_id_chatbot_session_id_fk" FOREIGN KEY ("session_id") REFERENCES "public"."chatbot_session"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_events" ADD CONSTRAINT "chatbot_events_message_id_chatbot_message_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."chatbot_message"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_instance" ADD CONSTRAINT "chatbot_instance_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_instance" ADD CONSTRAINT "chatbot_instance_provider_id_chatbot_provider_id_fk" FOREIGN KEY ("provider_id") REFERENCES "public"."chatbot_provider"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_message" ADD CONSTRAINT "chatbot_message_chatbot_session_id_chatbot_session_id_fk" FOREIGN KEY ("chatbot_session_id") REFERENCES "public"."chatbot_session"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_message" ADD CONSTRAINT "chatbot_message_chatbot_user_id_chatbot_users_id_fk" FOREIGN KEY ("chatbot_user_id") REFERENCES "public"."chatbot_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_message_with_geolocation" ADD CONSTRAINT "chatbot_message_with_geolocation_message_id_chatbot_message_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."chatbot_message"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_provider" ADD CONSTRAINT "chatbot_provider_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_session" ADD CONSTRAINT "chatbot_session_chatbot_user_id_chatbot_users_id_fk" FOREIGN KEY ("chatbot_user_id") REFERENCES "public"."chatbot_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_session" ADD CONSTRAINT "chatbot_session_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_users" ADD CONSTRAINT "chatbot_users_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_users" ADD CONSTRAINT "chatbot_users_chatbot_instance_id_chatbot_instance_id_fk" FOREIGN KEY ("chatbot_instance_id") REFERENCES "public"."chatbot_instance"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot" ADD CONSTRAINT "chatbot_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispatch_assignment" ADD CONSTRAINT "dispatch_assignment_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispatch_assignment" ADD CONSTRAINT "dispatch_assignment_operator_id_operators_id_fk" FOREIGN KEY ("operator_id") REFERENCES "public"."operators"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispatch_assignment" ADD CONSTRAINT "dispatch_assignment_ride_id_rides_id_fk" FOREIGN KEY ("ride_id") REFERENCES "public"."rides"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "driver_vehicle" ADD CONSTRAINT "driver_vehicle_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "driver_vehicle" ADD CONSTRAINT "driver_vehicle_driver_id_users_id_fk" FOREIGN KEY ("driver_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "driver_vehicle" ADD CONSTRAINT "driver_vehicle_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "geodata_entries" ADD CONSTRAINT "geodata_entries_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "geodata_entries" ADD CONSTRAINT "geodata_entries_address_id_addresses_id_fk" FOREIGN KEY ("address_id") REFERENCES "public"."addresses"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "geodata_entries" ADD CONSTRAINT "geodata_entries_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "geodata_entries" ADD CONSTRAINT "geodata_entries_area_id_areas_id_fk" FOREIGN KEY ("area_id") REFERENCES "public"."areas"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "i18n_translations" ADD CONSTRAINT "i18n_translations_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "map_provider" ADD CONSTRAINT "map_provider_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_chat_id_chat_id_fk" FOREIGN KEY ("chat_id") REFERENCES "public"."chat"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_from_user_id_users_id_fk" FOREIGN KEY ("from_user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_to_user_id_users_id_fk" FOREIGN KEY ("to_user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_related_pbx_call_id_pbx_call_id_fk" FOREIGN KEY ("related_pbx_call_id") REFERENCES "public"."pbx_call"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_replied_to_message_id_messages_id_fk" FOREIGN KEY ("replied_to_message_id") REFERENCES "public"."messages"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "multi_tenant_group" ADD CONSTRAINT "multi_tenant_group_parent_group_id_multi_tenant_group_id_fk" FOREIGN KEY ("parent_group_id") REFERENCES "public"."multi_tenant_group"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_extensions" ADD CONSTRAINT "operator_extensions_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_extensions" ADD CONSTRAINT "operator_extensions_user_tenant_id_user_tenant_id_fk" FOREIGN KEY ("user_tenant_id") REFERENCES "public"."user_tenant"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_shift" ADD CONSTRAINT "operator_shift_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_shift" ADD CONSTRAINT "operator_shift_operator_id_operators_id_fk" FOREIGN KEY ("operator_id") REFERENCES "public"."operators"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_performance_stats" ADD CONSTRAINT "operator_performance_stats_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_performance_stats" ADD CONSTRAINT "operator_performance_stats_operator_user_id_users_id_fk" FOREIGN KEY ("operator_user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operators" ADD CONSTRAINT "operators_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operators" ADD CONSTRAINT "operators_user_tenant_id_user_tenant_id_fk" FOREIGN KEY ("user_tenant_id") REFERENCES "public"."user_tenant"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment" ADD CONSTRAINT "payment_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment" ADD CONSTRAINT "payment_ride_id_rides_id_fk" FOREIGN KEY ("ride_id") REFERENCES "public"."rides"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment" ADD CONSTRAINT "payment_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call_additional_operators" ADD CONSTRAINT "pbx_call_additional_operators_pbx_call_id_pbx_call_id_fk" FOREIGN KEY ("pbx_call_id") REFERENCES "public"."pbx_call"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call_additional_operators" ADD CONSTRAINT "pbx_call_additional_operators_operator_id_operators_id_fk" FOREIGN KEY ("operator_id") REFERENCES "public"."operators"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_operator_id_operators_id_fk" FOREIGN KEY ("operator_id") REFERENCES "public"."operators"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_ride_id_rides_id_fk" FOREIGN KEY ("ride_id") REFERENCES "public"."rides"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_area_id_areas_id_fk" FOREIGN KEY ("area_id") REFERENCES "public"."areas"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_instances" ADD CONSTRAINT "pbx_instances_default_tenant_id_tenants_id_fk" FOREIGN KEY ("default_tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promos" ADD CONSTRAINT "promos_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promotion" ADD CONSTRAINT "promotion_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "ride_event" ADD CONSTRAINT "ride_event_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "ride_event" ADD CONSTRAINT "ride_event_ride_id_ride_order_id_fk" FOREIGN KEY ("ride_id") REFERENCES "public"."ride_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "ride_rating" ADD CONSTRAINT "ride_rating_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rides" ADD CONSTRAINT "rides_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rides" ADD CONSTRAINT "rides_passenger_id_users_id_fk" FOREIGN KEY ("passenger_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rides" ADD CONSTRAINT "rides_driver_id_users_id_fk" FOREIGN KEY ("driver_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rides" ADD CONSTRAINT "rides_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "roles" ADD CONSTRAINT "roles_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scheduled_calls" ADD CONSTRAINT "scheduled_calls_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scheduled_calls" ADD CONSTRAINT "scheduled_calls_created_by_operator_id_users_id_fk" FOREIGN KEY ("created_by_operator_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scheduled_calls" ADD CONSTRAINT "scheduled_calls_customer_user_id_users_id_fk" FOREIGN KEY ("customer_user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sms_log" ADD CONSTRAINT "sms_log_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sms_log" ADD CONSTRAINT "sms_log_operator_user_id_users_id_fk" FOREIGN KEY ("operator_user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sms_log" ADD CONSTRAINT "sms_log_related_pbx_call_id_pbx_call_id_fk" FOREIGN KEY ("related_pbx_call_id") REFERENCES "public"."pbx_call"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "system_settings" ADD CONSTRAINT "system_settings_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_billing_profile" ADD CONSTRAINT "tenant_billing_profile_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_bots" ADD CONSTRAINT "tenant_bots_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_localization" ADD CONSTRAINT "tenant_localization_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_settings" ADD CONSTRAINT "tenant_settings_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenants" ADD CONSTRAINT "tenants_multi_tenant_group_id_multi_tenant_group_id_fk" FOREIGN KEY ("multi_tenant_group_id") REFERENCES "public"."multi_tenant_group"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_bot_roles" ADD CONSTRAINT "user_bot_roles_bot_id_bots_id_fk" FOREIGN KEY ("bot_id") REFERENCES "public"."bots"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_bot_roles" ADD CONSTRAINT "user_bot_roles_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_consent" ADD CONSTRAINT "user_consent_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_consent" ADD CONSTRAINT "user_consent_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_identity" ADD CONSTRAINT "user_identity_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_kyc" ADD CONSTRAINT "user_kyc_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_kyc" ADD CONSTRAINT "user_kyc_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_onboarding" ADD CONSTRAINT "user_onboarding_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_onboarding" ADD CONSTRAINT "user_onboarding_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_profile_history" ADD CONSTRAINT "user_profile_history_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_profile_history" ADD CONSTRAINT "user_profile_history_changed_by_id_users_id_fk" FOREIGN KEY ("changed_by_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tenant" ADD CONSTRAINT "user_tenant_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tenant" ADD CONSTRAINT "user_tenant_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tenant" ADD CONSTRAINT "user_tenant_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tenant" ADD CONSTRAINT "user_tenant_invited_by_id_users_id_fk" FOREIGN KEY ("invited_by_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle" ADD CONSTRAINT "vehicle_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_expense" ADD CONSTRAINT "vehicle_expense_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_expense" ADD CONSTRAINT "vehicle_expense_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_note" ADD CONSTRAINT "vehicle_note_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_note" ADD CONSTRAINT "vehicle_note_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_odometer_log" ADD CONSTRAINT "vehicle_odometer_log_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_odometer_log" ADD CONSTRAINT "vehicle_odometer_log_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_parts_log" ADD CONSTRAINT "vehicle_parts_log_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_parts_log" ADD CONSTRAINT "vehicle_parts_log_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_reminder" ADD CONSTRAINT "vehicle_reminder_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_reminder" ADD CONSTRAINT "vehicle_reminder_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_repair" ADD CONSTRAINT "vehicle_repair_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_repair" ADD CONSTRAINT "vehicle_repair_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_service_record" ADD CONSTRAINT "vehicle_service_record_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_service_record" ADD CONSTRAINT "vehicle_service_record_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_upgrade" ADD CONSTRAINT "vehicle_upgrade_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_upgrade" ADD CONSTRAINT "vehicle_upgrade_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "verification_event" ADD CONSTRAINT "verification_event_chatbot_user_id_chatbot_users_id_fk" FOREIGN KEY ("chatbot_user_id") REFERENCES "public"."chatbot_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "wallet" ADD CONSTRAINT "wallet_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "wallet" ADD CONSTRAINT "wallet_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "webhook_subscriber" ADD CONSTRAINT "webhook_subscriber_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "addresses_tenant_id_idx" ON "addresses" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "addresses_postal_code_country_idx" ON "addresses" USING btree ("postal_code","country_code");--> statement-breakpoint
CREATE INDEX "addresses_geom_idx" ON "addresses" USING gist ("geom") WHERE "addresses"."geom" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "areas_tenant_id_idx" ON "areas" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "areas_geom_idx" ON "areas" USING gist ("geom");--> statement-breakpoint
CREATE INDEX "areas_deleted_at_idx" ON "areas" USING btree ("deleted_at") WHERE "areas"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "audit_logs_tenant_id_idx" ON "audit_logs" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "audit_logs_actor_id_actor_type_idx" ON "audit_logs" USING btree ("actor_id","actor_type");--> statement-breakpoint
CREATE INDEX "audit_logs_event_type_idx" ON "audit_logs" USING btree ("event_type");--> statement-breakpoint
CREATE INDEX "audit_logs_target_table_target_id_idx" ON "audit_logs" USING btree ("target_table","target_id");--> statement-breakpoint
CREATE INDEX "audit_logs_created_at_idx" ON "audit_logs" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "audit_logs_deleted_at_idx" ON "audit_logs" USING btree ("deleted_at") WHERE "audit_logs"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "bots_webhook_path_segment_idx" ON "bots" USING btree ("webhook_path_segment");--> statement-breakpoint
CREATE INDEX "chatbot_events_chatbot_instance_id_idx" ON "chatbot_events" USING btree ("chatbot_instance_id");--> statement-breakpoint
CREATE INDEX "chatbot_events_user_id_idx" ON "chatbot_events" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "chatbot_events_session_id_idx" ON "chatbot_events" USING btree ("session_id");--> statement-breakpoint
CREATE INDEX "chatbot_events_message_id_idx" ON "chatbot_events" USING btree ("message_id");--> statement-breakpoint
CREATE INDEX "chatbot_events_event_type_idx" ON "chatbot_events" USING btree ("event_type");--> statement-breakpoint
CREATE INDEX "chatbot_events_occurred_at_idx" ON "chatbot_events" USING btree ("occurred_at");--> statement-breakpoint
CREATE INDEX "chatbot_events_deleted_at_idx" ON "chatbot_events" USING btree ("deleted_at") WHERE "chatbot_events"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "chatbot_message_session_id_idx" ON "chatbot_message" USING btree ("chatbot_session_id");--> statement-breakpoint
CREATE INDEX "chatbot_message_user_id_idx" ON "chatbot_message" USING btree ("chatbot_user_id");--> statement-breakpoint
CREATE INDEX "chatbot_message_sent_at_idx" ON "chatbot_message" USING btree ("sent_at");--> statement-breakpoint
CREATE INDEX "chatbot_message_geolocation_idx" ON "chatbot_message_with_geolocation" USING gist ("location");--> statement-breakpoint
CREATE INDEX "chatbot_session_chatbot_user_id_idx" ON "chatbot_session" USING btree ("chatbot_user_id");--> statement-breakpoint
CREATE INDEX "chatbot_session_tenant_id_idx" ON "chatbot_session" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "chatbot_session_geolocation_idx" ON "chatbot_session" USING gist ("geolocation") WHERE "chatbot_session"."geolocation" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "provider_user_id_chatbot_instance_idx" ON "chatbot_users" USING btree ("provider_user_id","chatbot_instance_id") WHERE "chatbot_users"."provider_user_id" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "user_id_chatbot_instance_idx" ON "chatbot_users" USING btree ("user_id","chatbot_instance_id");--> statement-breakpoint
CREATE INDEX "dispatch_assignment_tenant_id_idx" ON "dispatch_assignment" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "dispatch_assignment_operator_ride_idx" ON "dispatch_assignment" USING btree ("operator_id","ride_id");--> statement-breakpoint
CREATE INDEX "driver_vehicles_tenant_id_idx" ON "driver_vehicle" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "geodata_entries_tenant_id_idx" ON "geodata_entries" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "geodata_entries_address_id_idx" ON "geodata_entries" USING btree ("address_id");--> statement-breakpoint
CREATE INDEX "geodata_entries_user_id_idx" ON "geodata_entries" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "geodata_entries_area_id_idx" ON "geodata_entries" USING btree ("area_id");--> statement-breakpoint
CREATE INDEX "geodata_entries_source_idx" ON "geodata_entries" USING btree ("source");--> statement-breakpoint
CREATE INDEX "geodata_entries_geom_idx" ON "geodata_entries" USING gist ("geom") WHERE "geodata_entries"."geom" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "geodata_entries_deleted_at_idx" ON "geodata_entries" USING btree ("deleted_at") WHERE "geodata_entries"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "i18n_translations_tenant_locale_namespace_key_idx" ON "i18n_translations" USING btree ("tenant_id","locale","namespace","key");--> statement-breakpoint
CREATE INDEX "i18n_translations_tenant_id_idx" ON "i18n_translations" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "i18n_translations_locale_idx" ON "i18n_translations" USING btree ("locale");--> statement-breakpoint
CREATE INDEX "i18n_translations_key_idx" ON "i18n_translations" USING btree ("key");--> statement-breakpoint
CREATE INDEX "i18n_translations_deleted_at_idx" ON "i18n_translations" USING btree ("deleted_at") WHERE "i18n_translations"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "invoices_tenant_id_due_date_idx" ON "invoices" USING btree ("tenant_id","due_date");--> statement-breakpoint
CREATE UNIQUE INDEX "map_provider_tenant_provider_idx" ON "map_provider" USING btree ("tenant_id","provider_name");--> statement-breakpoint
CREATE UNIQUE INDEX "map_provider_access_token_idx" ON "map_provider" USING btree ("access_token");--> statement-breakpoint
CREATE INDEX "messages_tenant_id_idx" ON "messages" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "messages_chat_id_idx" ON "messages" USING btree ("chat_id");--> statement-breakpoint
CREATE INDEX "messages_from_user_id_idx" ON "messages" USING btree ("from_user_id");--> statement-breakpoint
CREATE INDEX "messages_to_user_id_idx" ON "messages" USING btree ("to_user_id");--> statement-breakpoint
CREATE INDEX "messages_sent_at_idx" ON "messages" USING btree ("sent_at");--> statement-breakpoint
CREATE INDEX "messages_message_type_idx" ON "messages" USING btree ("message_type");--> statement-breakpoint
CREATE INDEX "messages_geolocation_idx" ON "messages" USING gist ("geolocation") WHERE "messages"."geolocation" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "messages_via_channel_idx" ON "messages" USING btree ("via_channel");--> statement-breakpoint
CREATE INDEX "messages_sms_status_idx" ON "messages" USING btree ("sms_status") WHERE "messages"."sms_status" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "messages_related_pbx_call_id_idx" ON "messages" USING btree ("related_pbx_call_id") WHERE "messages"."related_pbx_call_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "messages_deleted_at_idx" ON "messages" USING btree ("deleted_at") WHERE "messages"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "name_unique_per_parent" ON "multi_tenant_group" USING btree ("parent_group_id","name");--> statement-breakpoint
CREATE INDEX "status_idx" ON "multi_tenant_group" USING btree ("status");--> statement-breakpoint
CREATE INDEX "operator_extensions_tenant_id_idx" ON "operator_extensions" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "operator_performance_stats_tenant_id_idx" ON "operator_performance_stats" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "operator_performance_stats_operator_user_id_idx" ON "operator_performance_stats" USING btree ("operator_user_id");--> statement-breakpoint
CREATE INDEX "operator_performance_stats_date_idx" ON "operator_performance_stats" USING btree ("date");--> statement-breakpoint
CREATE INDEX "operator_performance_stats_shift_id_idx" ON "operator_performance_stats" USING btree ("shift_id") WHERE "operator_performance_stats"."shift_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "operator_performance_stats_tenant_operator_date_idx" ON "operator_performance_stats" USING btree ("tenant_id","operator_user_id","date");--> statement-breakpoint
CREATE INDEX "operators_tenant_id_idx" ON "operators" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "payment_processor_ref_idx" ON "payment" USING btree ("processor_ref");--> statement-breakpoint
CREATE UNIQUE INDEX "payment_user_status_idx" ON "payment" USING btree ("user_id","status");--> statement-breakpoint
CREATE INDEX "pbx_call_tenant_id_idx" ON "pbx_call" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "pbx_call_user_id_idx" ON "pbx_call" USING btree ("user_id") WHERE "pbx_call"."user_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_operator_id_idx" ON "pbx_call" USING btree ("operator_id") WHERE "pbx_call"."operator_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_ride_id_idx" ON "pbx_call" USING btree ("ride_id") WHERE "pbx_call"."ride_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_status_idx" ON "pbx_call" USING btree ("status");--> statement-breakpoint
CREATE INDEX "pbx_call_started_at_idx" ON "pbx_call" USING btree ("started_at");--> statement-breakpoint
CREATE INDEX "pbx_call_from_phone_number_idx" ON "pbx_call" USING btree ("from_phone_number") WHERE "pbx_call"."from_phone_number" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_to_phone_number_idx" ON "pbx_call" USING btree ("to_phone_number") WHERE "pbx_call"."to_phone_number" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "pbx_call_tenant_external_pbx_id_uk" ON "pbx_call" USING btree ("tenant_id","external_pbx_id") WHERE "pbx_call"."external_pbx_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_source_idx" ON "pbx_call" USING btree ("source") WHERE "pbx_call"."source" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_assigned_at_idx" ON "pbx_call" USING btree ("assigned_at") WHERE "pbx_call"."assigned_at" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_language_code_idx" ON "pbx_call" USING btree ("language_code") WHERE "pbx_call"."language_code" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_customer_phone_idx" ON "pbx_call" USING btree ("customer_phone_number") WHERE "pbx_call"."customer_phone_number" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_area_id_idx" ON "pbx_call" USING btree ("area_id") WHERE "pbx_call"."area_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_numeric_extension_idx" ON "pbx_call" USING btree ("numeric_extension") WHERE "pbx_call"."numeric_extension" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "promo_code_idx" ON "promos" USING btree ("code");--> statement-breakpoint
CREATE INDEX "ride_event_tenant_id_idx" ON "ride_event" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "ride_event_ride_idx" ON "ride_event" USING btree ("ride_id");--> statement-breakpoint
CREATE UNIQUE INDEX "ride_event_type_idx" ON "ride_event" USING btree ("event_type");--> statement-breakpoint
CREATE UNIQUE INDEX "ride_order_tenant_idx" ON "ride_order" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "ride_order_passenger_idx" ON "ride_order" USING btree ("passenger_id");--> statement-breakpoint
CREATE UNIQUE INDEX "ride_order_driver_idx" ON "ride_order" USING btree ("driver_id");--> statement-breakpoint
CREATE INDEX "rides_tenant_id_idx" ON "rides" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "roles_tenant_id_name_idx" ON "roles" USING btree ("tenant_id","name");--> statement-breakpoint
CREATE INDEX "scheduled_calls_tenant_id_idx" ON "scheduled_calls" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "scheduled_calls_created_by_operator_id_idx" ON "scheduled_calls" USING btree ("created_by_operator_id");--> statement-breakpoint
CREATE INDEX "scheduled_calls_customer_user_id_idx" ON "scheduled_calls" USING btree ("customer_user_id");--> statement-breakpoint
CREATE INDEX "scheduled_calls_phone_number_idx" ON "scheduled_calls" USING btree ("phone_number");--> statement-breakpoint
CREATE INDEX "scheduled_calls_next_activation_timestamp_idx" ON "scheduled_calls" USING btree ("next_activation_timestamp");--> statement-breakpoint
CREATE INDEX "scheduled_calls_is_active_idx" ON "scheduled_calls" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "scheduled_calls_status_idx" ON "scheduled_calls" USING btree ("status");--> statement-breakpoint
CREATE INDEX "sms_log_tenant_id_idx" ON "sms_log" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "sms_log_direction_idx" ON "sms_log" USING btree ("direction");--> statement-breakpoint
CREATE INDEX "sms_log_from_number_idx" ON "sms_log" USING btree ("from_number");--> statement-breakpoint
CREATE INDEX "sms_log_to_number_idx" ON "sms_log" USING btree ("to_number");--> statement-breakpoint
CREATE INDEX "sms_log_status_idx" ON "sms_log" USING btree ("status") WHERE "sms_log"."status" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "sms_log_operator_user_id_idx" ON "sms_log" USING btree ("operator_user_id") WHERE "sms_log"."operator_user_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "sms_log_related_pbx_call_id_idx" ON "sms_log" USING btree ("related_pbx_call_id") WHERE "sms_log"."related_pbx_call_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "sms_log_logged_at_idx" ON "sms_log" USING btree ("logged_at");--> statement-breakpoint
CREATE UNIQUE INDEX "system_settings_key_tenant_id_idx" ON "system_settings" USING btree ("key","tenant_id");--> statement-breakpoint
CREATE INDEX "system_settings_tenant_id_idx" ON "system_settings" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "system_settings_key_idx" ON "system_settings" USING btree ("key");--> statement-breakpoint
CREATE INDEX "system_settings_deleted_at_idx" ON "system_settings" USING btree ("deleted_at") WHERE "system_settings"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_billing_profile_tenant_id_idx" ON "tenant_billing_profile" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_billing_profile_tax_id_unique" ON "tenant_billing_profile" USING btree ("tax_id");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_billing_profile_default_per_tenant" ON "tenant_billing_profile" USING btree ("tenant_id","default_profile");--> statement-breakpoint
CREATE UNIQUE INDEX "telegram_bot_token_idx" ON "tenant_bots" USING btree ("telegram_bot_token");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_localization_tenant_id_index" ON "tenant_localization" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_settings_tenant_id_key_unique" ON "tenant_settings" USING btree ("tenant_id","key");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_settings_tenant_id_idx" ON "tenant_settings" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_settings_key_idx" ON "tenant_settings" USING btree ("key");--> statement-breakpoint
CREATE UNIQUE INDEX "user_consent_user_consent_type_idx" ON "user_consent" USING btree ("user_id","consent_type");--> statement-breakpoint
CREATE UNIQUE INDEX "user_consent_deleted_at_idx" ON "user_consent" USING btree ("deleted_at");--> statement-breakpoint
CREATE UNIQUE INDEX "user_identity_provider_external_id_idx" ON "user_identity" USING btree ("provider","external_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_identity_user_id_idx" ON "user_identity" USING btree ("user_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_identity_provider_idx" ON "user_identity" USING btree ("provider");--> statement-breakpoint
CREATE INDEX "user_kyc_tenant_id_idx" ON "user_kyc" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_kyc_user_id_idx" ON "user_kyc" USING btree ("user_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_onboarding_user_tenant_idx" ON "user_onboarding" USING btree ("user_id","tenant_id");--> statement-breakpoint
CREATE INDEX "user_onboarding_deleted_at_idx" ON "user_onboarding" USING btree ("deleted_at");--> statement-breakpoint
CREATE UNIQUE INDEX "user_profile_history_user_id_changed_at_idx" ON "user_profile_history" USING btree ("user_id","changed_at");--> statement-breakpoint
CREATE UNIQUE INDEX "user_profile_history_deleted_at_idx" ON "user_profile_history" USING btree ("deleted_at") WHERE "user_profile_history"."deleted_at" is not null;--> statement-breakpoint
CREATE UNIQUE INDEX "user_tenant_user_id_tenant_id_idx" ON "user_tenant" USING btree ("user_id","tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_tenant_invited_by_id_idx" ON "user_tenant" USING btree ("invited_by_id");--> statement-breakpoint
CREATE UNIQUE INDEX "users_phone_idx" ON "users" USING btree ("phone");--> statement-breakpoint
CREATE UNIQUE INDEX "users_email_idx" ON "users" USING btree ("email");--> statement-breakpoint
CREATE UNIQUE INDEX "users_external_id_idx" ON "users" USING btree ("external_id");--> statement-breakpoint
CREATE INDEX "users_deleted_at_idx" ON "users" USING btree ("deleted_at") WHERE "users"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "users_current_location_idx" ON "users" USING gist ("current_location") WHERE "users"."current_location" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "license_plate_tenant_id_idx" ON "vehicle" USING btree ("license_plate","tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_expense_tenant_id_idx" ON "vehicle_expense" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_expense_vehicle_id_idx" ON "vehicle_expense" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_expense_type_idx" ON "vehicle_expense" USING btree ("expense_type");--> statement-breakpoint
CREATE INDEX "vehicle_expense_date_idx" ON "vehicle_expense" USING btree ("date");--> statement-breakpoint
CREATE INDEX "vehicle_note_tenant_id_idx" ON "vehicle_note" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_note_vehicle_id_idx" ON "vehicle_note" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_note_created_at_idx" ON "vehicle_note" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "vehicle_odometer_log_tenant_id_idx" ON "vehicle_odometer_log" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_odometer_log_vehicle_id_idx" ON "vehicle_odometer_log" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_odometer_log_date_idx" ON "vehicle_odometer_log" USING btree ("date");--> statement-breakpoint
CREATE INDEX "vehicle_parts_log_tenant_id_idx" ON "vehicle_parts_log" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_parts_log_vehicle_id_idx" ON "vehicle_parts_log" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_parts_log_date_idx" ON "vehicle_parts_log" USING btree ("date");--> statement-breakpoint
CREATE INDEX "vehicle_parts_log_part_number_idx" ON "vehicle_parts_log" USING btree ("part_number");--> statement-breakpoint
CREATE INDEX "vehicle_parts_log_supplier_idx" ON "vehicle_parts_log" USING btree ("supplier");--> statement-breakpoint
CREATE INDEX "vehicle_reminder_tenant_id_idx" ON "vehicle_reminder" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_reminder_vehicle_id_idx" ON "vehicle_reminder" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_reminder_urgency_idx" ON "vehicle_reminder" USING btree ("urgency");--> statement-breakpoint
CREATE INDEX "vehicle_reminder_trigger_date_idx" ON "vehicle_reminder" USING btree ("trigger_date");--> statement-breakpoint
CREATE INDEX "vehicle_reminder_is_dismissed_idx" ON "vehicle_reminder" USING btree ("is_dismissed");--> statement-breakpoint
CREATE INDEX "vehicle_repair_tenant_id_idx" ON "vehicle_repair" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_repair_vehicle_id_idx" ON "vehicle_repair" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_repair_date_idx" ON "vehicle_repair" USING btree ("date");--> statement-breakpoint
CREATE INDEX "vehicle_service_record_tenant_id_idx" ON "vehicle_service_record" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_service_record_vehicle_id_idx" ON "vehicle_service_record" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_service_record_date_idx" ON "vehicle_service_record" USING btree ("date");--> statement-breakpoint
CREATE INDEX "vehicle_upgrade_tenant_id_idx" ON "vehicle_upgrade" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_upgrade_vehicle_id_idx" ON "vehicle_upgrade" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_upgrade_date_idx" ON "vehicle_upgrade" USING btree ("date");--> statement-breakpoint
CREATE UNIQUE INDEX "wallet_user_tenant_currency_idx" ON "wallet" USING btree ("user_id","tenant_id","currency");