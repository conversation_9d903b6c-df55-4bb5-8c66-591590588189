import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const RIDE_RATINGS_VALUES = ["ONE", "TWO", "THREE", "FOUR", "FIVE"] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const rideRatingsEnum = pgEnum("ride_ratings_enum", RIDE_RATINGS_VALUES);

// 3. Define TypeScript union type for strict type safety
export type RideRatingsType = (typeof RIDE_RATINGS_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum RideRatings {
	ONE = "ONE",
	TWO = "TWO",
	THREE = "THREE",
	FOUR = "FOUR",
	FIVE = "FIVE",
}
