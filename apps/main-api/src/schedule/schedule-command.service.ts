// apps/main-api/src/schedule/schedule-command.service.ts
import { RascalService } from "@/rabbitmq/rascal.service"; // Removed .js
import { Injectable, Logger } from "@nestjs/common";
import type {
	ScheduleCreateRequestedPayload,
	ScheduleDetails,
} from "@repo/types";
import { v4 as uuidv4 } from "uuid";

@Injectable()
export class ScheduleCommandService {
	private readonly logger = new Logger(ScheduleCommandService.name);

	constructor(private readonly rascalService: RascalService) {}

	async requestScheduleCreation(
		tenantId: string,
		scheduleDetails: ScheduleDetails,
		requestingActor: ScheduleCreateRequestedPayload["actor"],
	): Promise<void> {
		const eventId = uuidv4();
		const eventPayload: ScheduleCreateRequestedPayload = {
			eventId,
			timestamp: new Date().toISOString(),
			version: "1.0",
			tenantId,
			sourceService: "ScheduleCommandService",
			actor: requestingActor,
			data: scheduleDetails,
		};

		try {
			await this.rascalService.publish(
				"pub_schedule_domain_event", // Ensure this matches your rascal-definitions.ts
				eventPayload,
				// Use a routing key context that matches your rascal definition for pub_schedule_domain_event
				{ routingKeyCtx: { action: "create_requested", tenantId } },
			);
			this.logger.log(
				`Published schedule.create.requested event: ${eventId} for tenant ${tenantId}`,
			);
		} catch (error) {
			this.logger.error(
				`Failed to publish schedule.create.requested for tenant ${tenantId}`,
				error,
			);
			throw error;
		}
	}
}