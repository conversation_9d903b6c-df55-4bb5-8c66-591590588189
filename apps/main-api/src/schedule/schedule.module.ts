// apps/main-api/src/schedule/schedule.module.ts
import { Module } from "@nestjs/common";
import { ScheduleCommandService } from "./schedule-command.service.js";
import { ScheduleEventHandlerService } from "./schedule-event-handler.service.js";
import { RascalModule } from "@/rabbitmq/rascal.module.js";
import { DatabaseModule } from "@/services/database/database.module.js";

@Module({
	imports: [
		RascalModule, // Import RascalModule to make RascalService available
		DatabaseModule, // Import DatabaseModule for ScheduleEventHandlerService's dependencies
	],
	controllers: [],
	providers: [
		ScheduleCommandService,
		ScheduleEventHandlerService, // Add ScheduleEventHandlerService as a provider
	],
	exports: [
		// ScheduleCommandService, // Export if needed by other modules
		// ScheduleEventHandlerService, // Export if needed by other modules
	],
})
export class ScheduleModule {}