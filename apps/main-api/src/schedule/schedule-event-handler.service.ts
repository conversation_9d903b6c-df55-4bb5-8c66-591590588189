// apps/main-api/src/schedule/schedule-event-handler.service.ts
import { RascalService, type MessageHandler } from "@/rabbitmq/rascal.service";
import { DatabaseService } from "@/services/database/database.service";
import { Injectable, Logger, type OnModuleInit } from "@nestjs/common";
import { type NewScheduledCall, scheduledCalls } from "@repo/db";
import type {
	ScheduleCreateRequestedPayload,
	ScheduleCreatedPayload,
} from "@repo/types";
import type { Message } from "amqplib"; // Message can remain a type import
import { v4 as uuidv4 } from "uuid";

@Injectable()
export class ScheduleEventHandlerService implements OnModuleInit {
	private readonly logger = new Logger(ScheduleEventHandlerService.name);

	constructor(
		private readonly rascalService: RascalService,
		private readonly dbService: DatabaseService,
	) {
		// DO NOT call subscribeToScheduleEvents() here
	}

	async onModuleInit() {
		this.logger.log(
			"ScheduleEventHandlerService initializing, subscribing to schedule events...",
		);
		await this.subscribeToScheduleEvents();
	}

	private async subscribeToScheduleEvents() {
		// Ensure AckOrNack is correctly typed for the callback
		type AckOrNackType = Parameters<MessageHandler>[2];

		try {
			await this.rascalService.subscribe("sub_schedule_create_requested", {
				message: async (
					_message: Message, // Type from amqplib
					content: ScheduleCreateRequestedPayload | null,
					ackOrNack: AckOrNackType, // Use the inferred or explicitly defined AckOrNack type
				) => {
					try {
						this.logger.log(
							`Consumed schedule.create.requested event: ${content?.eventId}`,
						);

						if (!content || !content.tenantId) {
							const errorMsg = `Received null content or missing tenantId for schedule.create.requested event ${content?.eventId}. Nacking without requeue.`;
							this.logger.error(errorMsg);
							return ackOrNack(new Error(errorMsg), {
								strategy: "nack",
								requeue: false,
							});
						}
						if (!content.data) {
							const errorMsg = `Received missing data for schedule.create.requested event ${content?.eventId}. Nacking without requeue.`;
							this.logger.error(errorMsg);
							return ackOrNack(new Error(errorMsg), {
								strategy: "nack",
								requeue: false,
							});
						}

						await this.handleScheduleCreateRequested(content);
						ackOrNack();
					} catch (error) {
						this.logger.error(
							`Error processing schedule.create.requested event ${content?.eventId}:`,
							error,
						);
						ackOrNack(error as Error, { strategy: "nack", requeue: false });
					}
				},
				error: (err: Error) => {
					this.logger.error(
						`Error on subscription 'sub_schedule_create_requested': ${err.message}`,
						err.stack,
					);
				},
				cancelled: (err?: Error) => {
					this.logger.warn(
						`Subscription 'sub_schedule_create_requested' was cancelled. ${err ? `Error: ${err.message}` : ""}`,
					);
				},
			});
			this.logger.log(
				"Successfully subscribed to 'sub_schedule_create_requested'.",
			);
		} catch (error) {
			this.logger.error(
				"Failed to subscribe to 'sub_schedule_create_requested'",
				error,
			);
			// Depending on the severity, you might want to rethrow or handle this to prevent app startup
		}
	}

	private async handleScheduleCreateRequested(
		payload: ScheduleCreateRequestedPayload,
	): Promise<void> {
		const { tenantId, data: scheduleDetails, actor } = payload;
		const db = this.dbService.getDb();

		const newScheduledCallData: NewScheduledCall = {
			// biome-ignore lint/style/noNonNullAssertion: tenantId is checked above
			tenantId: tenantId!,
			customerUserId: scheduleDetails.passengerUserId,
			status: "PENDING", // Assuming scheduled calls are initially PENDING
			scheduledTimestamp: new Date(scheduleDetails.scheduledTime), // Ensure scheduledTime is a valid date string
			baseCallDetails: {
				type: "scheduled",
				// Map scheduleDetails to baseCallDetails
				pickupAddress: scheduleDetails.pickupAddress,
				pickupLatLng: scheduleDetails.pickupLatLng ?? null,
				dropoffLatLng: scheduleDetails.dropoffLatLng ?? null,
				dropoffAddress: scheduleDetails.dropoffAddress ?? null,
				notes: scheduleDetails.notes ?? null,
				scheduledBy: actor,
			},
		};

		const [createdScheduledCall] = await db
			.insert(scheduledCalls)
			.values(newScheduledCallData)
			.returning();

		this.logger.log(
			`Created scheduled PbxCall record: ${createdScheduledCall.id} for tenant ${tenantId}`,
		);
		const scheduleCreatedEvent: ScheduleCreatedPayload = {
			eventId: uuidv4(),
			timestamp: new Date().toISOString(),
			version: "1.0",
			tenantId,
			sourceService: "ScheduleEventHandlerService",
			correlationId: payload.eventId,
			actor: payload.actor,
			data: {
				...createdScheduledCall,
			},
		};

		await this.rascalService.publish(
			"pub_schedule_domain_event", // Use the correct publication name from rascal-definitions.ts
			scheduleCreatedEvent,
			{
				routingKeyCtx: {
					action: "created",
					tenantId,
					scheduleId: createdScheduledCall.id,
				},
			},
		);
		this.logger.log(
			`Published schedule.created event: ${scheduleCreatedEvent.eventId}`,
		);
	}
}
