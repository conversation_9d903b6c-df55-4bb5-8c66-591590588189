const { readFileSync, readdirSync, writeFileSync } = require("node:fs");
const { join } = require("node:path");

const generateI18nTypes = () => {
	const localesDir = join(__dirname, "..", "locales");
	const files = readdirSync(localesDir, { withFileTypes: true });
	const allLangs: string[] = [];
	const fileKeySet = new Set<string>();

	for (const file of files) {
		if (file.isDirectory()) {
			allLangs.push(file.name);
			const langFiles = readdirSync(join(localesDir, file.name), {
				withFileTypes: true,
			});
			for (const langFile of langFiles) {
				if (langFile.isFile() && langFile.name.endsWith(".json")) {
					const content = JSON.parse(
						readFileSync(join(localesDir, file.name, langFile.name), "utf8"),
					);
					for (const k of flattenKeys(content)) {
						fileKeySet.add(k);
					}
				}
			}
		}
	}
	const typesContent = `// THIS FILE IS AUTO-GENERATED. DO NOT EDIT MANUALLY.
export type I18nKeys = ${Array.from(fileKeySet)
 .map((key) => `"${key}"`)
 .join(" | ")};

export type I18nTranslations = {
 ${allLangs.map((lang) => `"${lang}": { [key in I18nKeys]: string };`).join("\n")}
};
`;

	writeFileSync(join(__dirname, "i18n.generated.ts"), typesContent, "utf8");
};

generateI18nTypes();
function flattenKeys(obj: any, prefix = ""): string[] {
	const keys: string[] = [];
	for (const key in obj) {
		if (Object.prototype.hasOwnProperty.call(obj, key)) {
			const value = obj[key];
			const newKey = prefix ? `${prefix}.${key}` : key;
			if (typeof value === "object" && value !== null) {
				keys.push(...flattenKeys(value, newKey));
			} else {
				keys.push(newKey);
			}
		}
	}
	return keys;
}
