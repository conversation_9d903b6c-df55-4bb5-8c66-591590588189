// apps/main-api/src/redis/redis.providers.ts
// Remove: import { ConfigService } from "@nestjs/config";
// Add:
import { AppConfigService } from "@/config/app-config.service.js"; // Import your custom AppConfigService

import Redis from "ioredis";
import { Logger } from "@nestjs/common";
import {
	REDIS_CACHE_CLIENT,
	REDIS_CLIENTS_PROVIDER,
	REDIS_PUBSUB_CLIENT,
} from "./redis.constants.js"; // Corrected import path if necessary

// Interface can remain as is, or be removed if not strictly needed by the factory's internal logic
interface RedisClientOptions {
	host: string | undefined;
	port: number | undefined;
	password?: string;
	db?: number;
	// max?: number; // 'max' is not a direct ioredis option, connection pooling is handled differently or by default
}

const logger = new Logger("RedisProviders");

export const redisClientsFactory = {
	provide: REDIS_CLIENTS_PROVIDER,
	// Change parameter from ConfigService to AppConfigService
	useFactory: (appConfigService: AppConfigService): { [key: string]: Redis } => {
		const baseConfig: RedisClientOptions = {
			// Use AppConfigService properties which include defaults
			host: appConfigService.redisHost,
			port: appConfigService.redisPort,
			password: appConfigService.redisPassword, // AppConfigService provides this, potentially undefined
		};

		// Debug log to confirm actual values used for Redis connection
		logger.debug(`[RedisFactory] baseConfig from AppConfigService: ${JSON.stringify({
			...baseConfig,
			password: baseConfig.password ? "***" : "(empty)",
		})}`);

		if (!baseConfig.host || !baseConfig.port) {
			// This check should ideally not be hit if AppConfigService provides defaults,
			// but it's good for robustness.
			throw new Error("Redis host and port are required (even after AppConfigService defaults).");
		}

		// Enhanced Redis client configuration
		const enhancedConfig = {
			host: baseConfig.host,
			port: baseConfig.port,
			...(baseConfig.password && { password: baseConfig.password }), // Conditionally add password
			// username: appConfigService.get<string>("REDIS_USERNAME"), // If you use REDIS_USERNAME, get it from AppConfigService
			connectTimeout: 10000,
			maxRetriesPerRequest: 3, // ioredis default is 20, adjust if needed
			enableReadyCheck: true,
			retryStrategy: (times: number) => {
				logger.warn(`Redis connection retry attempt ${times}`);
				const delay = Math.min(times * 100, 3000); // Exponential backoff with max 3s
				return delay;
			},
			reconnectOnError: (err: Error) => {
				const targetError = "READONLY";
				if (err.message.includes(targetError)) {
					// Only reconnect on specific errors
					return true;
				}
				return false; // Default: do not reconnect on all errors
			},
		};

		const cacheClient = new Redis({
			...enhancedConfig,
			db: appConfigService.redisDb, // Use redisDb getter from AppConfigService
			connectionName: "cacheClient", // Naming for clarity in logs
		});

		const pubsubClient = new Redis({
			...enhancedConfig,
			db: appConfigService.redisDb, // Assuming same DB for pubsub, or use a different getter if configured
			connectionName: "pubsubClient", // Naming for clarity in logs
		});

		// Optional: Add event listeners
		for (const client of [cacheClient, pubsubClient]) {
			client.on("error", (err: Error) =>
				logger.error(`Redis client (${(client.options as any).connectionName}) error:`, err.stack),
			);
			client.on("connect", () =>
				logger.log(`Redis client (${(client.options as any).connectionName}) connected to ${client.options.host}:${client.options.port}`),
			);
		}

		return {
			[REDIS_CACHE_CLIENT]: cacheClient,
			[REDIS_PUBSUB_CLIENT]: pubsubClient,
		};
	},
	// Change injection from ConfigService to AppConfigService
	inject: [AppConfigService],
};