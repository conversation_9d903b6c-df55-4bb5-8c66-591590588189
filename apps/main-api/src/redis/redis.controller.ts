// apps/main-api/src/redis/redis.controller.ts
import {
    Body,
    Controller,
    Delete,
    Get,
    Inject,
    Param,
    Post,
    Query,
} from "@nestjs/common";
// Change this to a value import because IRedisService is now an abstract class
import { IRedisService } from "./redis.interface.js";
import { RedisServiceToken } from "./redis.token.js";

@Controller("redis")
export class RedisController {
    constructor(
        @Inject(RedisServiceToken)
        private readonly redisService: IRedisService, // Type is now an abstract class
    ) {}

    // ... rest of the controller methods
    @Get("health")
    async checkHealth(): Promise<{ status: string; error?: string }> {
        return this.redisService.checkHealth();
    }

    @Get("cache/:tenantId/:key")
    async getCache(
        @Param("tenantId") tenantId: string,
        @Param("key") key: string,
    ): Promise<string | null> {
        return this.redisService.get(tenantId, key);
    }

    @Post("cache/:tenantId/:key")
    async setCache(
        @Param("tenantId") tenantId: string,
        @Param("key") key: string,
        @Body("value") value: string,
        @Query("ttl") ttl?: number,
    ): Promise<void> {
        await this.redisService.set(tenantId, key, value, ttl);
    }

    @Delete("cache/:tenantId/:key")
    async delCache(
        @Param("tenantId") tenantId: string,
        @Param("key") key: string,
    ): Promise<void> {
        await this.redisService.del(tenantId, key);
    }

    @Post("publish/:channel")
    async publish(
        @Param("channel") channel: string,
        @Body("message") message: string,
    ): Promise<{ status: string }> {
        await this.redisService.publish(channel, message);
        return { status: `Published to ${channel}` };
    }

    @Post("subscribe/:channel")
    async subscribe(
        @Param("channel") channel: string,
        // The callback type here is problematic for a POST body.
        // Subscribing via HTTP POST with a callback function in the body is not typical.
        // This endpoint might need rethinking if it's intended for external use.
        // For now, keeping the signature as is to focus on the TS1272 error.
        @Body("callback") callback: (channel: string, message: string) => void,
    ): Promise<{ status: string }> {
        await this.redisService.subscribe(channel, callback);
        return { status: `Subscribed to ${channel}` };
    }

    @Delete("subscribe/:channel")
    async unsubscribe(
        @Param("channel") channel: string,
    ): Promise<{ status: string }> {
        await this.redisService.unsubscribe(channel);
        return { status: `Unsubscribed from ${channel}` };
    }
}