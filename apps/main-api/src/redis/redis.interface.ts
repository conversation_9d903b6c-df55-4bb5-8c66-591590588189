import type { Redis } from "ioredis";

// Change from interface to abstract class
export abstract class IRedisService {
	// Basic Key-Value Operations
	abstract get(tenantId: string, key: string): Promise<string | null>;
	abstract set(
		tenantId: string,
		key: string,
		value: string | number,
		ttl?: number,
	): Promise<void>;
	abstract del(tenantId: string, key: string): Promise<void>;
	abstract exists(tenantId: string, key: string | string[]): Promise<number>;
	abstract expire(
		tenantId: string,
		key: string,
		seconds: number,
	): Promise<number>;
	abstract ttl(tenantId: string, key: string): Promise<number>;
	abstract keys(tenantId: string, pattern: string): Promise<string[]>;
	abstract incr(tenantId: string, key: string): Promise<number>;
	abstract decr(tenantId: string, key: string): Promise<number>;

	// Hash Operations
	abstract hGet(
		tenantId: string,
		key: string,
		field: string,
	): Promise<string | null>;
	abstract hSet(
		tenantId: string,
		key: string,
		field: string,
		value: string | number,
	): Promise<number>;
	abstract hDel(
		tenantId: string,
		key: string,
		field: string | string[],
	): Promise<number>;
	abstract hGetAll(
		tenantId: string,
		key: string,
	): Promise<Record<string, string>>;
	abstract hmSet(
		tenantId: string,
		key: string,
		data: Record<string, string | number>,
	): Promise<"OK">;
	abstract hExists(
		tenantId: string,
		key: string,
		field: string,
	): Promise<number>;
	abstract hIncrBy(
		tenantId: string,
		key: string,
		field: string,
		increment: number,
	): Promise<number>;

	// Set Operations
	abstract sAdd(
		tenantId: string,
		key: string,
		members: (string | number) | (string | number)[],
	): Promise<number>;
	abstract sRem(
		tenantId: string,
		key: string,
		members: (string | number) | (string | number)[],
	): Promise<number>;
	abstract sMembers(tenantId: string, key: string): Promise<string[]>;
	abstract sIsMember(
		tenantId: string,
		key: string,
		member: string | number,
	): Promise<number>;

	// Sorted Set Operations
	abstract zAdd(
		tenantId: string,
		key: string,
		score: number,
		member: string | number,
	): Promise<number>;
	abstract zAddMultiple(
		tenantId: string,
		key: string,
		members: (string | number | { score: number; member: string | number })[],
	): Promise<number>;
	abstract zRange(
		tenantId: string,
		key: string,
		start: number,
		stop: number,
		withScores?: "WITHSCORES",
	): Promise<string[]>;
	abstract zRem(
		tenantId: string,
		key: string,
		members: (string | number) | (string | number)[],
	): Promise<number>;
	abstract zScore(
		tenantId: string,
		key: string,
		member: string | number,
	): Promise<string | null>;
	abstract zIncrBy(
		tenantId: string,
		key: string,
		increment: number,
		member: string | number,
	): Promise<string>;

	// Pub/Sub
	abstract publish(channel: string, message: string): Promise<number>;
	abstract subscribe(
		channel: string,
		callback: (channel: string, message: string) => unknown,
	): Promise<void>;
	abstract unsubscribe(channel: string): Promise<void>;

	// Utility
	abstract checkHealth(): Promise<{ status: string; error?: string }>;
	abstract getCacheClient(): Redis;
}
