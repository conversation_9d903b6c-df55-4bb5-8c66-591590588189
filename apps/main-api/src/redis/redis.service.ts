import {
	Injectable,
	Logger,
	type OnM<PERSON>ule<PERSON><PERSON>roy,
	type OnModuleInit,
} from "@nestjs/common";
// Use string literals instead of ambient const enums
type RedisStatus =
	| "DISCONNECTED"
	| "CONNECTING"
	| "CONNECTED"
	| "DISCONNECTING"
	| "RECONNECTING";
// biome-ignore lint/style/useImportType: <explanation>
import { AppConfigService } from "@/config/app-config.service.js";
import { Redis, RedisOptions } from "ioredis";

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
	public client!: Redis;
	public status: RedisStatus = "DISCONNECTED";
	private readonly logger = new Logger(RedisService.name);

	constructor(private readonly appConfigService: AppConfigService) {}

	async onModuleInit() {
		this.logger.log(
			`Attempting to connect to Redis at ${this.appConfigService.redisHost}...`,
		);
		const redisOptions: RedisOptions = {
			host: this.appConfigService.redisHost,
			// port: this.appConfigService.redisPort, // Add if configurable
			// password: this.appConfigService.redisPassword, // Add if configurable
			maxRetriesPerRequest: 3,
			enableReadyCheck: true,
			connectTimeout: 10000, // 10 seconds
			lazyConnect: false, // Try to connect immediately
			retryStrategy: (times: number): number | null => {
				if (times > 5) {
					this.logger.error(
						`Redis: Exhausted initial connection retry attempts (${times}). Giving up.`,
					);
					this.status = "DISCONNECTED";
					return null; // Stop retrying
				}
				const delay: number = Math.min(times * 200, 3000); // Exponential backoff
				this.logger.warn(
					`Redis: Connection attempt ${times} failed. Retrying in ${delay}ms...`,
				);
				this.status = "RECONNECTING";
				return delay;
			},
		};

		// Only create Redis client if host is configured
		if (this.appConfigService.redisHost) {
			this.client = new Redis(redisOptions);

			this.client.on("connect", () => {
				this.logger.log("Redis: Successfully connected.");
				this.status = "CONNECTED";
			});

			this.client.on("ready", () => {
				this.logger.log("Redis: Client is ready.");
				this.status = "CONNECTED";
			});

			this.client.on("error", (err) => {
				// Don't log as error during initial connection phase
				this.logger.warn("Redis Connection Error:", err.message);
				this.status = "DISCONNECTED";
			});

			this.client.on("reconnecting", (delay: number) => {
				this.logger.warn(`Redis: Reconnecting (delay: ${delay}ms)...`);
				this.status = "RECONNECTING";
			});

			this.client.on("close", () => {
				this.logger.warn("Redis: Connection closed.");
				this.status = "DISCONNECTED";
			});

			this.client.on("end", (): void => {
				this.logger.warn(
					"Redis: Connection ended (client will not try to reconnect).",
				);
				this.status = "DISCONNECTED";
			});
		} else {
			this.logger.warn(
				"Redis host not configured - Redis service will be disabled",
			);
			this.status = "DISCONNECTED";
		}
		// No longer block startup on Redis connection
	}

	async onModuleDestroy() {
		if (this.client) {
			this.logger.log("Redis: Disconnecting client...");
			await this.client.quit().catch((err) => {
				this.logger.error("Redis: Error during quit:", err.message);
				this.client.disconnect();
			});
			this.logger.log("Redis: Client disconnected.");
		}
	}
}
