import { AppConfigService } from "@/config/app-config.service.js";
import { ConfigModule } from "@/config/config.module.js";
import { Global, Module } from "@nestjs/common";
import { Logger } from "@nestjs/common";
import { ClientsModule, Transport } from "@nestjs/microservices";
import { redisClientsFactory } from "./redis.providers.js"; // Import the factory
import { RedisDataService } from "./redis-data.service.js";
import { RedisServiceToken } from "./redis.token.js";
import { REDIS_CACHE_CLIENT, REDIS_CLIENTS_PROVIDER, REDIS_PUBSUB_CLIENT } from "./redis.constants.js";
import { Redis } from "ioredis";

export const REDIS_MICROSERVICE_CLIENT = "REDIS_MICROSERVICE_CLIENT";

@Global()
@Module({
	imports: [
		ConfigModule,
		ClientsModule.registerAsync([
			{
				name: REDIS_MICROSERVICE_CLIENT,
				inject: [AppConfigService],
				useFactory: (config: AppConfigService) => {
					const logger = new Logger("RedisModule");
					const options = {
						host: config.redisHost,
						port: config.redisPort,
						...(config.redisPassword ? { password: config.redisPassword } : {}),
						db: config.redisDb,
						keyPrefix: config.redisPrefix,
						retryAttempts: 5,
						retryDelay: 3000,
					};
					logger.log(
						`Connecting to Redis at ${options.host}:${options.port} (db: ${options.db}, prefix: "${options.keyPrefix}")`,
					);
					return {
						transport: Transport.REDIS,
						options,
					};
				},
			},
		]),
	],
	providers: [
		redisClientsFactory, // This provides REDIS_CLIENTS_PROVIDER
		{
			provide: REDIS_CACHE_CLIENT,
			useFactory: (clients: { [key: string]: Redis }) => clients[REDIS_CACHE_CLIENT],
			inject: [REDIS_CLIENTS_PROVIDER],
		},
		{
			provide: REDIS_PUBSUB_CLIENT,
			useFactory: (clients: { [key: string]: Redis }) => clients[REDIS_PUBSUB_CLIENT],
			inject: [REDIS_CLIENTS_PROVIDER],
		},
		RedisDataService,
		{
			provide: RedisServiceToken,
			useClass: RedisDataService,
		},
	],
	exports: [
		RedisServiceToken,
		RedisDataService,
		ClientsModule,
		REDIS_CACHE_CLIENT,
		REDIS_PUBSUB_CLIENT,
	],
})
export class RedisModule {}
