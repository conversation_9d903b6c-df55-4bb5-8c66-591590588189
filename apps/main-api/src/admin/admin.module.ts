import { Module } from '@nestjs/common';
import { AdminController } from './admin.controller.js';
import { AdminService } from './admin.service.js';
import { DatabaseModule } from '@/services/database/database.module.js';
import { AuthModule } from '@/auth/auth.module.js';
import { ConfigModule } from '@/config/config.module.js';

@Module({
  imports: [DatabaseModule, AuthModule, ConfigModule],
  controllers: [AdminController],
  providers: [AdminService],
})
export class AdminModule {}
