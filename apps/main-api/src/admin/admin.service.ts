
import {
	Inject,
	Injectable,
	Logger,
	NotFoundException,
} from "@nestjs/common";
import {
	type DbType,
	type NewPbxInstance,
	type NewTenant,
	type PbxConnectionDetails,
	type PbxInstance,
	type Tenant,
	type TenantRoutingRule,
	eq,
	pbxInstances,
	tenants,
	PbxType,
	PbxTenantAssociation,
} from "@repo/db";

import type { CreatePbxInstanceDto } from "./dto/create-pbx-instance.dto.js";
import type { CreateTenantDto, UpdateTenantDto } from "./dto/create-tenant.dto.js";
import type { PbxInstanceListItemDto } from "./dto/pbx-instance-list.dto.js";
import type { UpdatePbxInstanceDto } from "./dto/update-pbx-instance.dto.js";

@Injectable()
export class AdminService {
	private readonly logger = new Logger(AdminService.name);

	constructor(
		@Inject("DRIZZLE_INSTANCE") private readonly db: DbType,
	) {}

	private encryptConnectionDetails(
		details:
			| CreatePbxInstanceDto["connectionDetails"]
			| UpdatePbxInstanceDto["connectionDetails"],
	): PbxConnectionDetails | Partial<PbxConnectionDetails> | undefined {
		if (!details) {
			return undefined;
		}
		// For now, just return the details as-is without encryption
		// TODO: Implement encryption when AppConfigService is properly injected
		return details as PbxConnectionDetails | Partial<PbxConnectionDetails>;
	}

	private decryptConnectionDetails(
		details: PbxConnectionDetails | Partial<PbxConnectionDetails> | undefined,
	): PbxConnectionDetails | Partial<PbxConnectionDetails> | undefined {
		if (!details) {
			return undefined;
		}
		// For now, just return the details as-is without decryption
		// TODO: Implement decryption when AppConfigService is properly injected
		return details;
	}

	async createPbxInstance(dto: CreatePbxInstanceDto): Promise<PbxInstance> {
		const dataToInsert: NewPbxInstance = {
			...dto,
			connectionDetails: this.encryptConnectionDetails(
				dto.connectionDetails,
			) as PbxConnectionDetails,
		};
		const [newInstance] = await this.db
			.insert(pbxInstances)
			.values(dataToInsert)
			.returning();
		return {
			...newInstance,
			connectionDetails: this.decryptConnectionDetails(
				newInstance.connectionDetails,
			) as PbxConnectionDetails, // Assert type after decryption
		};
	}

	async findAllPbxInstances(): Promise<PbxInstanceListItemDto[]> {
		const instances = await this.db.select().from(pbxInstances);
		return instances.map((inst) => ({
			...inst,
			// Mask sensitive connection details for list view
			connectionDetails: {
				...(inst.connectionDetails as Record<string, any>),
				password: (inst.connectionDetails as any)?.password
					? "*****"
					: undefined,
				apiKey: (inst.connectionDetails as any)?.apiKey ? "*****" : undefined,
				clientSecret: (inst.connectionDetails as any)?.clientSecret
					? "*****"
					: undefined, // Added missing colon and undefined
				basicAuthPassword: (inst.connectionDetails as any)?.basicAuthPassword
					? "*****"
					: undefined,
			},
		}));
	}
	async findOnePbxInstance(id: string): Promise<PbxInstance | undefined> {
		const instance = await this.db.query.pbxInstances.findFirst({
			where: eq(pbxInstances.id, id),
		});
		return instance
			? {
					...instance,
					connectionDetails: this.decryptConnectionDetails(
						instance.connectionDetails,
					) as PbxConnectionDetails, // Assert type after decryption
				}
			: undefined;
	}

	async updatePbxInstance(
		id: string,
		dto: UpdatePbxInstanceDto,
	): Promise<PbxInstance> {
		const updateData: Partial<Omit<NewPbxInstance, "id" | "createdAt">> = {};

		// Map defined DTO fields to updateData
		if (dto.name !== undefined) updateData.name = dto.name;
		if (dto.type !== undefined) updateData.type = dto.type;
		if (dto.isEnabled !== undefined) updateData.isEnabled = dto.isEnabled;
		if (dto.tenantAssociationType !== undefined)
			updateData.tenantAssociationType = dto.tenantAssociationType;
		// Allow setting defaultTenantId to null
		if (Object.prototype.hasOwnProperty.call(dto, "defaultTenantId")) {
			updateData.defaultTenantId =
				dto.defaultTenantId === null ? undefined : dto.defaultTenantId;
		} else if (dto.defaultTenantId !== undefined) updateData.defaultTenantId = dto.defaultTenantId;
		if (dto.tenantRoutingRules !== undefined) {
			// Ensure DTO rules are compatible with schema rules or map them
			updateData.tenantRoutingRules = dto.tenantRoutingRules as
				| TenantRoutingRule[]
				| TenantRoutingRule[]
				| null;
		}
		if (dto.connectionDetails) {
			updateData.connectionDetails = this.encryptConnectionDetails(
				dto.connectionDetails, // Cast to the non-partial type for encryption
			) as PbxConnectionDetails | undefined; // Cast back to the non-partial type for insertion
		}
		const [updatedInstance] = await this.db
			.update(pbxInstances)
			.set(updateData)
			.where(eq(pbxInstances.id, id))
			.returning();
		if (!updatedInstance) {
			throw new NotFoundException(`PbxInstance with ID ${id} not found`);
		}
		return {
			...updatedInstance,
			connectionDetails: this.decryptConnectionDetails(
				updatedInstance.connectionDetails,
			) as PbxConnectionDetails,
		}; // Assert type
	}

	async deletePbxInstance(id: string): Promise<void> {
		await this.db.delete(pbxInstances).where(eq(pbxInstances.id, id));
	}

	getPbxConfigOptions() {
		return {
			pbxTypes: Object.values(PbxType),
			pbxTenantAssociationTypes: Object.values(PbxTenantAssociation),
		};
	}

	// --- Tenant CRUD Methods ---
	async createTenant(data: CreateTenantDto): Promise<Tenant> {
		const [newTenant] = await this.db
			.insert(tenants)
			.values(data as NewTenant)
			.returning();
		if (!newTenant) {
			throw new Error("Failed to create tenant");
		}
		return newTenant;
	}

	async findAllTenants(): Promise<Tenant[]> {
		return this.db.select().from(tenants);
	}

	async findOneTenant(id: string): Promise<Tenant | undefined> {
		return this.db.query.tenants.findFirst({ where: eq(tenants.id, id) });
	}

	async updateTenant(id: string, data: UpdateTenantDto): Promise<Tenant> {
		const updateData: Partial<Omit<NewTenant, "id" | "createdAt">> = {};

		if (data.name !== undefined) updateData.name = data.name;
		if (data.status !== undefined) updateData.status = data.status;
		if (data.plan !== undefined) updateData.plan = data.plan;

		const [updatedTenant] = await this.db
			.update(tenants)
			.set(updateData)
			.where(eq(tenants.id, id))
			.returning();

		if (!updatedTenant) {
			throw new NotFoundException(`Tenant with ID ${id} not found`);
		}

		return updatedTenant;
	}

	async deleteTenant(id: string): Promise<void> {
		const result = await this.db.delete(tenants).where(eq(tenants.id, id)).returning();
		if (result.length === 0) {
			throw new NotFoundException(`Tenant with ID ${id} not found`);
		}
	}
}
