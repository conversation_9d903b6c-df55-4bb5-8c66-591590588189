import type { AppConfigService } from "@/config/app-config.service";
import {
	Injectable,
	Logger,
	NotFoundException,
	NotImplementedException,
} from "@nestjs/common";
import {
	type DbType,
	type NewPbxInstance,
	type NewTenant,
	type PbxConnectionDetails,
	type PbxInstance,
	type Tenant,
	type TenantRoutingRule,
	eq,
	pbxInstances,
	tenants,
} from "@repo/db";
import { decryptBotToken, encryptBotToken } from "@repo/db";
import type { CreatePbxInstanceDto } from "./dto/create-pbx-instance.dto.js";
import type { CreateTenantDto, UpdateTenantDto } from "./dto/create-tenant.dto.js";
import type { PbxInstanceListItemDto } from "./dto/pbx-instance-list.dto.js";
import type { UpdatePbxInstanceDto } from "./dto/update-pbx-instance.dto.js";

@Injectable()
export class AdminService {
	private readonly logger = new Logger(AdminService.name);

	constructor(
		private readonly db: DbType,
		private readonly appConfigService: AppConfigService,
	) {}

	private encryptConnectionDetails(
		details:
			| CreatePbxInstanceDto["connectionDetails"]
			| UpdatePbxInstanceDto["connectionDetails"],
	): PbxConnectionDetails | Partial<PbxConnectionDetails> | undefined {
		if (!details) {
			return undefined;
		}
		const newDetails = { ...details } as any; // Work with a copy

		// Implement logic to encrypt sensitive parts of connectionDetails
		if (
			"password" in newDetails &&
			typeof newDetails.password === "string" &&
			newDetails.password
		) {
			newDetails.password = encryptBotToken(
				newDetails.password,
				this.appConfigService.tokenEncryptionKey,
			);
		}
		if (
			"apiKey" in newDetails &&
			typeof newDetails.apiKey === "string" &&
			newDetails.apiKey
		) {
			newDetails.apiKey = encryptBotToken(
				newDetails.apiKey,
				this.appConfigService.tokenEncryptionKey,
			);
		}
		if (
			"clientSecret" in newDetails &&
			typeof newDetails.clientSecret === "string" &&
			newDetails.clientSecret
		) {
			newDetails.clientSecret = encryptBotToken(
				newDetails.clientSecret,
				this.appConfigService.tokenEncryptionKey,
			);
		}
		if (
			"basicAuthPassword" in newDetails &&
			typeof newDetails.basicAuthPassword === "string" &&
			newDetails.basicAuthPassword
		) {
			newDetails.basicAuthPassword = encryptBotToken(
				newDetails.basicAuthPassword,
				this.appConfigService.tokenEncryptionKey,
			);
		}
		return newDetails as PbxConnectionDetails | Partial<PbxConnectionDetails>;
	}

	private decryptConnectionDetails(
		details: PbxConnectionDetails | Partial<PbxConnectionDetails> | undefined,
	): PbxConnectionDetails | Partial<PbxConnectionDetails> | undefined {
		if (!details) {
			return undefined;
		}
		const mutableDetails = { ...details }; // Work with a copy

		// Implement logic to decrypt sensitive parts when needed
		try {
			if (
				mutableDetails.type === "ASTERISK_ARI" &&
				"password" in mutableDetails &&
				typeof mutableDetails.password === "string"
			) {
				const decryptedPassword = decryptBotToken(
					mutableDetails.password,
					this.appConfigService.tokenEncryptionKey,
				);
				mutableDetails.password = decryptedPassword === null ? undefined : decryptedPassword;
			}
			if (
				mutableDetails.type === "FREEPBX_GRAPHQL" &&
				"apiKey" in mutableDetails &&
				typeof mutableDetails.apiKey === "string"
			) {
				const decryptedApiKey = decryptBotToken(
					mutableDetails.apiKey,
					this.appConfigService.tokenEncryptionKey,
				);
				mutableDetails.apiKey = decryptedApiKey === null ? undefined : decryptedApiKey;
			}
			if (
				mutableDetails.type === "FREEPBX_GRAPHQL" &&
				"clientSecret" in mutableDetails &&
				typeof mutableDetails.clientSecret === "string"
			) {
				const decryptedClientSecret = decryptBotToken(
					mutableDetails.clientSecret,
					this.appConfigService.tokenEncryptionKey,
				);
				mutableDetails.clientSecret = decryptedClientSecret === null ? undefined : decryptedClientSecret;
			}
			if (
				mutableDetails.type === "FREEPBX_GRAPHQL" &&
				"basicAuthPassword" in mutableDetails &&
				typeof mutableDetails.basicAuthPassword === "string"
			) {
				const decryptedBasicAuthPassword = decryptBotToken(
					mutableDetails.basicAuthPassword,
					this.appConfigService.tokenEncryptionKey,
				);
				mutableDetails.basicAuthPassword = decryptedBasicAuthPassword === null ? undefined : decryptedBasicAuthPassword;
			}
		} catch (e) {
			// Log decryption errors but don't prevent returning the object
			this.logger.warn(
				`Failed to decrypt sensitive data for PBX instance: ${(details as PbxInstance)?.id || "N/A"}`,
				e,
			);
			// Return original details if decryption fails to avoid exposing potentially incorrect data
			// or return mutableDetails with failed fields still encrypted, depending on desired behavior
			// For now, we return the mutableDetails which might have some fields decrypted and some not.
			// A more robust approach might involve marking fields as failed decryption.
		}
		// Return the modified object. If no decryption happened or failed, it's the same as the input.
		return mutableDetails;
	}

	async createPbxInstance(dto: CreatePbxInstanceDto): Promise<PbxInstance> {
		const dataToInsert: NewPbxInstance = {
			...dto,
			connectionDetails: this.encryptConnectionDetails(
				dto.connectionDetails,
			) as PbxConnectionDetails,
		};
		const [newInstance] = await this.db
			.insert(pbxInstances)
			.values(dataToInsert)
			.returning();
		return {
			...newInstance,
			connectionDetails: this.decryptConnectionDetails(
				newInstance.connectionDetails,
			) as PbxConnectionDetails, // Assert type after decryption
		};
	}

	async findAllPbxInstances(): Promise<PbxInstanceListItemDto[]> {
		const instances = await this.db.select().from(pbxInstances);
		return instances.map((inst) => ({
			...inst,
			// Mask sensitive connection details for list view
			connectionDetails: {
				...(inst.connectionDetails as Record<string, any>),
				password: (inst.connectionDetails as any)?.password
					? "*****"
					: undefined,
				apiKey: (inst.connectionDetails as any)?.apiKey ? "*****" : undefined,
				clientSecret: (inst.connectionDetails as any)?.clientSecret
					? "*****"
					: undefined, // Added missing colon and undefined
				basicAuthPassword: (inst.connectionDetails as any)?.basicAuthPassword
					? "*****"
					: undefined,
			},
		}));
	}
	async findOnePbxInstance(id: string): Promise<PbxInstance | undefined> {
		const instance = await this.db.query.pbxInstances.findFirst({
			where: eq(pbxInstances.id, id),
		});
		return instance
			? {
					...instance,
					connectionDetails: this.decryptConnectionDetails(
						instance.connectionDetails,
					) as PbxConnectionDetails, // Assert type after decryption
				}
			: undefined;
	}

	async updatePbxInstance(
		id: string,
		dto: UpdatePbxInstanceDto,
	): Promise<PbxInstance> {
		const updateData: Partial<Omit<NewPbxInstance, "id" | "createdAt">> = {};

		// Map defined DTO fields to updateData
		if (dto.name !== undefined) updateData.name = dto.name;
		if (dto.type !== undefined) updateData.type = dto.type;
		if (dto.isEnabled !== undefined) updateData.isEnabled = dto.isEnabled;
		if (dto.tenantAssociationType !== undefined)
			updateData.tenantAssociationType = dto.tenantAssociationType;
		// Allow setting defaultTenantId to null
		if (Object.prototype.hasOwnProperty.call(dto, "defaultTenantId")) {
			updateData.defaultTenantId =
				dto.defaultTenantId === null ? undefined : dto.defaultTenantId;
		} else if (dto.defaultTenantId !== undefined) updateData.defaultTenantId = dto.defaultTenantId;
		if (dto.tenantRoutingRules !== undefined) {
			// Ensure DTO rules are compatible with schema rules or map them
			updateData.tenantRoutingRules = dto.tenantRoutingRules as
				| TenantRoutingRule[]
				| TenantRoutingRule[]
				| null;
		}
		if (dto.connectionDetails) {
			updateData.connectionDetails = this.encryptConnectionDetails(
				dto.connectionDetails, // Cast to the non-partial type for encryption
			) as PbxConnectionDetails | undefined; // Cast back to the non-partial type for insertion
		}
		const [updatedInstance] = await this.db
			.update(pbxInstances)
			.set(updateData)
			.where(eq(pbxInstances.id, id))
			.returning();
		if (!updatedInstance) {
			throw new NotFoundException(`PbxInstance with ID ${id} not found`);
		}
		return {
			...updatedInstance,
			connectionDetails: this.decryptConnectionDetails(
				updatedInstance.connectionDetails,
			) as PbxConnectionDetails,
		}; // Assert type
	}

	async deletePbxInstance(id: string): Promise<void> {
		await this.db.delete(pbxInstances).where(eq(pbxInstances.id, id));
	}

	// --- Tenant CRUD Methods ---
	async createTenant(data: CreateTenantDto): Promise<Tenant> {
		const [newTenant] = await this.db
			.insert(tenants)
			.values(data as NewTenant)
			.returning();
		if (!newTenant) {
			throw new Error("Failed to create tenant");
		}
		return newTenant;
	}

	async findAllTenants(): Promise<Tenant[]> {
		return this.db.select().from(tenants);
	}

	async findOneTenant(id: string): Promise<Tenant | undefined> {
		return this.db.query.tenants.findFirst({ where: eq(tenants.id, id) });
	}

	async updateTenant(id: string, data: UpdateTenantDto): Promise<Tenant> {
		throw new NotImplementedException(
			`Updating tenant with ID ${id} using data: ${JSON.stringify(data)}`,
		);
	}

	async deleteTenant(id: string): Promise<void> {
		throw new NotImplementedException(`Deleting tenant with ID ${id}`);
	}
}
