import {
	Body,
	Controller,
	Delete,
	Get,
	Param,
	Post,
	Put,
	UseGuards,
	UsePipes,
	ValidationPipe,
} from "@nestjs/common";
import type { PbxInstance, Tenant } from "@repo/db";
import { DevApiKeyGuard } from "@/auth/dev-api-key.guard.js";
import type { AdminService } from "./admin.service";
import type { CreatePbxInstanceDto } from "./dto/create-pbx-instance.dto.js";
import type { CreateTenantDto, UpdateTenantDto } from "./dto/create-tenant.dto.js";
import type { PbxInstanceListItemDto } from "./dto/pbx-instance-list.dto.js";
import type { UpdatePbxInstanceDto } from "./dto/update-pbx-instance.dto.js";

@Controller("admin") // Base path for all admin routes
@UseGuards(DevApiKeyGuard) // Require development API key for all admin routes
@UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true, transform: true })) // Apply validation globally
export class AdminController {
	constructor(private readonly adminService: AdminService) {}

	// --- PBX Instances ---
	@Post("pbx-instances")
	createPbxInstance(
		@Body() createPbxInstanceDto: CreatePbxInstanceDto,
	): Promise<PbxInstance> {
		// Return type is PbxInstance from @repo/db
		return this.adminService.createPbxInstance(createPbxInstanceDto);
	}

	@Get("pbx-instances")
	findAllPbxInstances(): Promise<PbxInstanceListItemDto[]> {
		return this.adminService.findAllPbxInstances();
	}

	@Get("pbx-instances/:id")
	findOnePbxInstance(
		@Param("id") id: string,
	): Promise<PbxInstance | undefined> {
		// Return type is PbxInstance | undefined from @repo/db
		return this.adminService.findOnePbxInstance(id);
	}

	@Put("pbx-instances/:id")
	updatePbxInstance(
		@Param("id") id: string,
		@Body() updatePbxInstanceDto: UpdatePbxInstanceDto,
	) {
		return this.adminService.updatePbxInstance(id, updatePbxInstanceDto);
	}

	@Delete("pbx-instances/:id")
	removePbxInstance(@Param("id") id: string) {
		return this.adminService.deletePbxInstance(id);
	}

	@Get("pbx-instances/config/options")
	getPbxConfigOptions() {
		return this.adminService.getPbxConfigOptions();
	}

	// --- Tenant Management ---
	@Post("tenants")
	createTenant(@Body() data: CreateTenantDto) {
		return this.adminService.createTenant(data);
	}

	@Get("tenants")
	findAllTenants(): Promise<Tenant[]> {
		return this.adminService.findAllTenants();
	}

	@Get("tenants/:id")
	findOneTenant(@Param("id") id: string): Promise<Tenant | undefined> {
		return this.adminService.findOneTenant(id);
	}

	@Put("tenants/:id")
	updateTenant(@Param("id") id: string, @Body() data: UpdateTenantDto) {
		return this.adminService.updateTenant(id, data);
	}

	@Delete("tenants/:id")
	removeTenant(@Param("id") id: string) {
		return this.adminService.deleteTenant(id);
	}
}
