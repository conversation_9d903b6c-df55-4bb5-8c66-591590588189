import { OmitType, PartialType } from '@nestjs/swagger';
import type { PbxInstance } from '@repo/db';

export class PbxInstanceListItemDto extends PartialType( // @ts-ignore: PbxInstance is a type, not a class
  OmitType(PbxInstance, ['connectionDetails'] as const)
) {
  connectionDetails?: Partial<PbxInstance['connectionDetails']> & {
    // These fields are included to explicitly show they are masked
    password?: string;
    apiKey?: string;
    clientSecret?: string;
    basicAuthPassword?: string;
  };
}
