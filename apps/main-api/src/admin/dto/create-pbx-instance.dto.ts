import * as db from "@repo/db"; // Assuming enums are exported
import { Type } from "class-transformer";
import {
	IsArray,
	IsBoolean,
	IsEnum,
	IsNotEmpty,
	IsObject,
	IsOptional,
	IsNumber,
	IsString,
	IsUUID,
	ValidateNested,
} from "class-validator";

class AriConnectionDetailsDto {
	@IsNotEmpty() 
	readonly type = 'ASTERISK_ARI' as const;
	@IsNotEmpty()
	@IsString()
	url!: string;

	@IsString()
	@IsOptional()
	username?: string;

	@IsString()
	@IsOptional()
	password?: string;

	@IsNotEmpty()
	@IsString()
	appName!: string;
}

class FreePbxConnectionDetailsDto {
	@IsNotEmpty() // @ts-ignore
	readonly type = 'FREEPBX_GRAPHQL' as const;

	@IsNotEmpty()
	@IsString()
	apiUrl!: string;

	@IsString()
	@IsOptional()
	clientId?: string;

	@IsString()
	@IsOptional()
	clientSecret?: string;

	@IsString()
	@IsOptional()
	tokenUrl?: string;

	@IsString()
	@IsOptional()
	apiKey?: string;
}

class TenantRoutingRuleDto {
	@IsNotEmpty() // @ts-ignore
	@IsString()
	type!: "DID" | "EXTENSION_PREFIX" | "QUEUE_NAME" | "CHANNEL_VAR_MATCH";

	@IsString()
	@IsOptional()
	pattern?: string;

	@IsString()
	@IsOptional()
	variableName?: string;

	@IsString()
	@IsOptional()
	variableValue?: string;

	@IsUUID()
	@IsNotEmpty()
	tenantId!: string;

	@IsNumber()
	@IsOptional()
	priority?: number;
}

export class CreatePbxInstanceDto {
	@IsString()
	@IsNotEmpty()
	name!: string;
	
	@IsNotEmpty()
	@IsEnum(db.PbxType)
	type!: db.PbxType;

	@IsNotEmpty()
	@IsBoolean()
	@IsOptional()
	isEnabled?: boolean;

	@IsObject()
	@ValidateNested()
	@Type(() => Object, {
		discriminator: { // @ts-ignore
			property: "type",
			subTypes: [ // Corrected: subTypes should be an array of objects
				{ value: AriConnectionDetailsDto, name: "ASTERISK_ARI" },
				{ value: FreePbxConnectionDetailsDto, name: "FREEPBX_GRAPHQL" },
			],
		},
	})
	connectionDetails!: AriConnectionDetailsDto | FreePbxConnectionDetailsDto;
	
	@IsEnum(db.pbxTenantAssociationTypeEnum)
	tenantAssociationType!: db.PbxTenantAssociationType;

	@IsUUID()
	@IsOptional()
	defaultTenantId?: string;

	@IsArray()
	@IsOptional()
	@ValidateNested({ each: true })
	@Type(() => TenantRoutingRuleDto)
	tenantRoutingRules?: TenantRoutingRuleDto[];
}
