import { Type } from "class-transformer";
import * as db from "@repo/db"; // Assuming enums are exported
import {
	IsArray,
	IsBoolean,
	IsEnum,
	IsNotEmpty,
	IsNumber,
	IsObject,
	IsOptional,
	IsString,
	IsUUID,
	ValidateNested,
} from "class-validator";

class AriConnectionDetailsDto {
	@IsNotEmpty()
	readonly type = "ASTERISK_ARI" as const;
	@IsOptional()
	@IsString()
	url?: string;

	@IsString()
	@IsOptional()
	username?: string;

	@IsString()
	@IsOptional()
	password?: string;

	@IsOptional()
	@IsString()
	appName?: string;
}

class FreePbxConnectionDetailsDto {
	@IsNotEmpty()
	readonly type = "FREEPBX_GRAPHQL" as const;

	@IsOptional()
	@IsString()
	apiUrl?: string;

	@IsString()
	@IsOptional()
	clientId?: string;

	@IsString()
	@IsOptional()
	clientSecret?: string;

	@IsString()
	@IsOptional()
	tokenUrl?: string;

	@IsString()
	@IsOptional()
	apiKey?: string;
}

class TenantRoutingRuleDto {
	@IsOptional()
	@IsString()
	type?: "DID" | "EXTENSION_PREFIX" | "QUEUE_NAME" | "CHANNEL_VAR_MATCH";

	@IsString()
	@IsOptional()
	pattern?: string;

	@IsString()
	@IsOptional()
	variableName?: string;

	@IsString()
	@IsOptional()
	variableValue?: string;

	@IsUUID()
	@IsOptional()
	tenantId?: string;

	@IsNumber()
	@IsOptional()
	priority?: number;
}

export class UpdatePbxInstanceDto {
	@IsString()
	@IsOptional()
	name?: string;

	@IsOptional()
	@IsEnum(db.PbxType)
	type?: db.PbxType;

	@IsBoolean()
	@IsOptional()
	isEnabled?: boolean;

	@IsObject()
	@IsOptional()
	@ValidateNested()
	@Type(() => Object, {
		discriminator: {
			property: "type",
			subTypes: [
				{ value: AriConnectionDetailsDto, name: "ASTERISK_ARI" },
				{ value: FreePbxConnectionDetailsDto, name: "FREEPBX_GRAPHQL" },
			],
		},
	})
	connectionDetails?: AriConnectionDetailsDto | FreePbxConnectionDetailsDto;

	@IsEnum(db.pbxTenantAssociationTypeEnum)
	@IsOptional()
	tenantAssociationType?: db.PbxTenantAssociationType;

	@IsUUID()
	@IsOptional()
	defaultTenantId?: string | null;

	@IsArray()
	@IsOptional()
	@ValidateNested({ each: true })
	@Type(() => TenantRoutingRuleDto)
	tenantRoutingRules?: TenantRoutingRuleDto[];
}
