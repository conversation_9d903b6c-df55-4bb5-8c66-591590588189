// apps/main-api/src/pbx/services/pbx-config.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/services/database/database.service';
import type { PbxInstanceConfigData } from '@repo/types';
import type { AriConnectionDetails, FreePbxGraphqlConnectionDetails } from '@repo/types';
import { pbxInstances } from '@repo/db';
// Define the interface locally since it's not exported from @repo/types


@Injectable()
export class PbxConfigService {
  private readonly logger = new Logger(PbxConfigService.name);
  private pbxConfigs = new Map<string, PbxInstanceConfigData>(); // pbxInstanceId -> Full Typed Config

  constructor(private readonly dbService: DatabaseService) {}

  async onModuleInit() {
    await this.loadConfigsFromDb();
  }

  async loadConfigsFromDb(): Promise<void> {
    this.logger.log('Loading PBX instance configurations from database...');
    const db = this.dbService.getDb();
    const configsFromDb: PbxInstanceConfigData[] = await db.select().from(pbxInstances) as PbxInstanceConfigData[];

    this.pbxConfigs.clear();
    for (const dbConfig of configsFromDb) {
      // Here, dbConfig.connectionDetails and dbConfig.tenantRoutingRules are raw JSON from DB
      // We trust Drizzle's .$type<>() to have informed TypeScript, but a runtime validation/parsing step
      // could be added here if the JSONB structure isn't guaranteed.
      this.pbxConfigs.set(dbConfig.id, {
        ...dbConfig,
        // Drizzle with .$type should handle the JSONB parsing to the specified type upon select.
        // If not, you'd parse them here:
        // connectionDetails: JSON.parse(dbConfig.connectionDetails as unknown as string) as PbxConnectionDetails,
        // tenantRoutingRules: dbConfig.tenantRoutingRules ? JSON.parse(dbConfig.tenantRoutingRules as unknown as string) as TenantRoutingRule[] : null,
      } as PbxInstanceConfigData); // Cast to ensure type alignment
    }
    this.logger.log(`Loaded ${this.pbxConfigs.size} enabled PBX configurations.`);
  }

  getPbxConfig(pbxInstanceId: string): PbxInstanceConfigData | undefined {
    return this.pbxConfigs.get(pbxInstanceId);
  }

  getAllAriConfigs(): PbxInstanceConfigData[] {
    return Array.from(this.pbxConfigs.values()).filter(
      (c): c is PbxInstanceConfigData & { connectionDetails: AriConnectionDetails } => // Type guard
        c.type === 'ASTERISK_ARI' && c.connectionDetails.type === 'ASTERISK_ARI'
    );
  }

  getAllFreePbxConfigs(): PbxInstanceConfigData[] {
    return Array.from(this.pbxConfigs.values()).filter(
      (c): c is PbxInstanceConfigData & { connectionDetails: FreePbxGraphqlConnectionDetails } => // Type guard
        c.type === 'FREEPBX_GRAPHQL' && c.connectionDetails.type === 'FREEPBX_GRAPHQL'
    );
  }
}