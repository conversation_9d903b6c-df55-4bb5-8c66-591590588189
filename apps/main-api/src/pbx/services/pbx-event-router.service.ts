import { RascalService } from "@/rabbitmq/rascal.service"; // Assuming this is a concrete service
// apps/main-api/src/pbx/services/pbx-event-router.service.ts
import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import type { AriChannelHangupEventPayload, FreePbxCdrReceivedEventPayload, RawPbxEventData, FreePbxCdr } from "@repo/types";
import type {
	PbxConfigService, // Corrected: PbxConfigService is imported from @repo/types
} from "./pbx-config.service";
// Assuming @ipcom/asterisk-ari is a placeholder for your actual ARI client library.
// If it's a real package and not installed, you'd need to install it for types.
// For now, we'll use placeholder types if the package isn't found.
type AriEvent = any; // Placeholder
type AriChannel = any; // Placeholder
import type { BaseEventPayload } from "@repo/types"; // Import BaseEventPayload from @repo/types
import { v4 as uuidv4 } from "uuid";
// import type { Event as AriEvent, Channel as AriChannel } from '@ipcom/asterisk-ari'; // Your client types
import type { PbxInstanceConfigData } from "@repo/types";

@Injectable()
export class PbxEventRouterService {
	private readonly logger = new Logger(PbxEventRouterService.name);

	constructor(
		private readonly pbxConfigService: PbxConfigService,
		private readonly rascalService: RascalService,
	) {}

	/**
	 * Top-level method to route a generic PBX event based on PBX type.
	 * This acts as a dispatcher to specific PBX handlers.
	 * @param pbxInstanceId The ID of the PBX instance the event originated from.
	 * @param eventTypeHint A string hint about the event type (e.g., 'CDR_RECEIVED', 'StasisStart').
	 * @param rawEventData The raw event payload received from the PBX webhook or source.
	 */
	async routeGenericPbxEvent(
		pbxInstanceId: string,
		eventTypeHint: string,
		rawEventData: any,
	): Promise<void> {
		const pbxConfig = this.pbxConfigService.getPbxConfig(pbxInstanceId);
		if (!pbxConfig) {
			this.logger.error(
				`No config found for PBX instance ID: ${pbxInstanceId}. Cannot route generic event.`,
			);
			throw new NotFoundException(
				`PBX Instance with ID ${pbxInstanceId} not found.`,
			);
		}

		this.logger.debug(
			`Routing generic event (hint: ${eventTypeHint}) for PBX ${pbxInstanceId} (Type: ${pbxConfig.type})`,
		);

		switch (pbxConfig.type) {
			case "ASTERISK_ARI":
				// Assuming rawEventData for ARI is the ARI Event object itself
				await this.routeAriEvent(
					pbxInstanceId,
					eventTypeHint as AriEvent["type"],
					rawEventData,
				);
				break;
			case "FREEPBX_GRAPHQL":
				await this.routeFreePbxEvent(
					pbxInstanceId,
					eventTypeHint as "CDR_RECEIVED" | string,
					rawEventData,
				);
				break;
			default:
				this.logger.warn(
					`Unsupported PBX type "${pbxConfig.type}" for PBX instance ${pbxInstanceId}. Cannot route event.`,
				);
		}
	}

	private resolveTenantId(
		pbxConfig: PbxInstanceConfigData, // Corrected type
		eventData: {
			channel?: AriChannel;
			dnid?: string;
			queueName?: string;
			[key: string]: any;
		},
	): string | null {
		if (
			pbxConfig.tenantAssociationType === "SINGLE_TENANT" &&
			pbxConfig.defaultTenantId
		) {
			return pbxConfig.defaultTenantId;
		}

		if (
			pbxConfig.tenantRoutingRules &&
			pbxConfig.tenantRoutingRules.length > 0
		) { // Corrected type
			// Sort rules by priority if you add a priority field
			for (const rule of pbxConfig.tenantRoutingRules) {
				switch (rule.type) {
					case "DID": { // Corrected: Was "DNID_PATTERN", should match TenantRoutingRule type 'DID'
						const did = eventData.dnid || eventData.channel?.dialplan?.dnid;
						if (
							did &&
							rule.pattern &&
							new RegExp(rule.pattern.replace("*", ".*")).test(did)
						)
							return rule.tenantId;
						break;
					}
					case "CHANNEL_VAR_MATCH": { // Corrected: Was "CHANNEL_VAR", should match TenantRoutingRule type 'CHANNEL_VAR_MATCH'
						if (
							rule.variableName && // Corrected: was varName
							eventData.channel?.variables?.[rule.variableName] && // Corrected: was varName
							(!rule.variableValue || // Corrected: was value
								eventData.channel.variables[rule.variableName] === rule.variableValue) // Corrected: was varName and value
						) {
							return rule.tenantId;
						}
						break;
					}
					case "QUEUE_NAME": {
						if ( // Corrected type
							rule.pattern &&
							eventData.queueName &&
							new RegExp(rule.pattern).test(eventData.queueName)
						) // Corrected type
							return rule.tenantId;
						break;
					}
					// Add EXTENSION_PREFIX if needed
				}
			}
		}
		this.logger.warn(
			`Could not resolve tenantId for PBX event on instance ${pbxConfig.id}. Event data: ${JSON.stringify(eventData).substring(0, 300)}`,
		);
		return pbxConfig.defaultTenantId || null; // Fallback to default if specified, otherwise null
	}

	private createBasePayload(
		pbxInstanceId: string,
		tenantId: string | null,
		sourceService: string,
	): Omit<BaseEventPayload, "data"> {
		return {
			eventId: uuidv4(),
			timestamp: new Date().toISOString(),
			version: "1.0",
			sourceService,
			tenantId,
			actor: { type: "PBX_GATEWAY", id: pbxInstanceId },
		};
	}

	// Made public as it's called by AriConnectorService and AriWebSocketClientImpl
	public async routeAriEvent(
		pbxInstanceId: string,
		eventType: AriEvent["type"],
		rawAriEvent: AriEvent & {
			channel?: AriChannel /* add other event specific data types from your client */;
		},
	): Promise<void> {
		const pbxConfig = this.pbxConfigService.getPbxConfig(pbxInstanceId);
		if (!pbxConfig) {
			throw new Error(`PBX config not found for ID: ${pbxInstanceId}`); // Should not happen if called from routeGenericPbxEvent
		}

		// Extract relevant data for tenant resolution, e.g., channel variables, DNID
		const resolutionContext: any = { channel: rawAriEvent.channel };
		if (rawAriEvent.channel?.dialplan?.dnid)
			resolutionContext.dnid = rawAriEvent.channel.dialplan.dnid;
		// Add more context if needed for your rules

		const tenantId = this.resolveTenantId(pbxConfig, resolutionContext);

		const pbxEventData: RawPbxEventData = {
			// This is from @repo/types
			pbxInternalId: pbxInstanceId,
			tenantId: tenantId, // tenantId is now part of pbxEventData
			externalCallId:
				rawAriEvent.channel?.id || (rawAriEvent as any).call?.id || "unknown", // Adjust based on event structure
			fromPhoneNumber: rawAriEvent.channel?.caller?.number,
			toDialedNumber:
				rawAriEvent.channel?.dialplan?.exten ||
				(rawAriEvent as any).endpoint?.resource,
			callerIdName: rawAriEvent.channel?.caller?.name,
			timestamp: rawAriEvent.timestamp || new Date().toISOString(),
			rawDetails: rawAriEvent,
		};

		let eventToPublish: BaseEventPayload | null = null;
		const base = this.createBasePayload(
			pbxInstanceId,
			tenantId,
			"AriConnectorService",
		);

		// Transform specific ARI events to your standardized Pbx...Event types
		switch (eventType) {
			case "StasisStart":
				eventToPublish = {
					...base,
					// Removed non-null assertion on channel, handle potential undefined in consumers
					data: {
						...pbxEventData,
						eventType: "ARI_STASIS_START",
						ariEvent: rawAriEvent,
						channel: rawAriEvent.channel,
						args: (rawAriEvent as any).args || [],
					},
				};
				break;
			case "ChannelHangupRequest": {
				// Assuming this is the hangup event from your client // Added block scope
				const hangupEvent = rawAriEvent as any; // Cast to access cause, cause_txt
				eventToPublish = {
					...base,
					// Removed non-null assertion on channel, handle potential undefined in consumers
					data: {
						...pbxEventData,
						eventType: "ARI_CHANNEL_HANGUP",
						ariEvent: rawAriEvent,
						channel: rawAriEvent.channel,
						cause: hangupEvent.cause,
						cause_txt: hangupEvent.cause_txt,
					},
				} as AriChannelHangupEventPayload;
				break;
			}
			case "ChannelStateChange": {
				if (rawAriEvent.channel?.state === "Up") {
					// Handle call answered event
					eventToPublish = {
						...base,
						data: {
							...pbxEventData,
							eventType: "ARI_CHANNEL_ANSWERED",
							ariEvent: rawAriEvent,
							channel: rawAriEvent.channel,
							timestamp: rawAriEvent.timestamp || new Date().toISOString(),
						},
					};
				}
				break;
			}
			case "ChannelDtmfReceived": {
				eventToPublish = {
					...base,
					data: {
						...pbxEventData,
						eventType: "ARI_DTMF_RECEIVED",
						ariEvent: rawAriEvent,
						digit: rawAriEvent.digit,
						durationMs: rawAriEvent.duration_ms,
					},
				};
				break;
			}
			default:
				this.logger.debug(
					`ARI Event type "${eventType}" not explicitly handled for routing. Raw data: ${JSON.stringify(rawAriEvent).substring(0, 200)}`,
				);
				// Optionally publish a generic event for unhandled types if needed for auditing
				// eventToPublish = { ...base, data: { ...pbxEventData, eventType: `ARI_GENERIC_${eventType}`, rawAriEvent } };
				return; // Or decide to publish a generic event
		}

		if (eventToPublish) {
			await this.rascalService.publish("pub_pbx_event", eventToPublish, {
				routingKeyCtx: {
					eventType: `pbx.ari.${eventType.toLowerCase()}`,
					tenantId: tenantId || pbxInstanceId,
				},
			});
			this.logger.log(
				`Routed ARI Event: ${eventType} for PBX ${pbxInstanceId}, Tenant ${tenantId || "N/A"}, EventID: ${eventToPublish.eventId}`,
			);
		}
	}

	// Made public for direct access from FreePbxGraphqlService
	async routeFreePbxEvent(
		pbxInstanceId: string,
		eventTypeHint: "CDR_RECEIVED" | string,
		rawGraphqlData: any,
	): Promise<void> {
		const pbxConfig = this.pbxConfigService.getPbxConfig(pbxInstanceId);
		if (!pbxConfig) {
			throw new Error(`PBX config not found for ID: ${pbxInstanceId}`); // Should not happen if called from routeGenericPbxEvent
		}

		// Extract relevant data for tenant resolution from rawGraphqlData (e.g., CDR's DID)
		const resolutionContext: any = {
			dnid: rawGraphqlData.did /* add other fields if needed */,
		};
		const tenantId = this.resolveTenantId(pbxConfig, resolutionContext);

		const pbxEventData: RawPbxEventData = {
			pbxInternalId: pbxInstanceId,
			tenantId: tenantId,
			externalCallId: rawGraphqlData.uniqueid || "unknown",
			fromPhoneNumber: rawGraphqlData.src,
			toDialedNumber: rawGraphqlData.dst,
			callerIdName: rawGraphqlData.clid?.match(/^(.*)<.*>$/)?.[1]?.trim(), // Extract name from "Name <Number>"
			timestamp: new Date(rawGraphqlData.calldate).toISOString(), // Assuming calldate is parsable
			rawDetails: rawGraphqlData,
		};

		let eventToPublish: BaseEventPayload | null = null;
		const base = this.createBasePayload(
			pbxInstanceId,
			tenantId,
			"FreePbxGraphqlService",
		);

		switch (eventTypeHint) {
			case "CDR_RECEIVED":
				eventToPublish = {
					...base,
					data: {
						...pbxEventData,
						eventType: "FREEPBX_GRAPHQL_CDR_RECEIVED",
						cdr: rawGraphqlData as FreePbxCdr,
					},
				} as FreePbxCdrReceivedEventPayload;
				break;
			// ... other GraphQL event types
			default:
				this.logger.warn(
					`FreePBX GraphQL Event type hint "${eventTypeHint}" not explicitly handled.`,
				);
				return;
		}

		if (eventToPublish) {
			await this.rascalService.publish("pub_pbx_event", eventToPublish, {
				routingKeyCtx: {
					eventType: `pbx.freepbx_graphql.${eventTypeHint.toLowerCase()}`,
					tenantId: tenantId || pbxInstanceId,
				},
			});
			this.logger.log(
				`Routed FreePBX Event: ${eventTypeHint} for PBX ${pbxInstanceId}, Tenant ${tenantId || "N/A"}, EventID: ${eventToPublish.eventId}`,
			);
		}
	}
}
