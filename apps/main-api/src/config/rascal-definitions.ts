// apps/main-api/src/config/rascal-definitions.ts
import type { BrokerConfig } from "rascal";

export const rascalDefinitions: BrokerConfig = {
	vhosts: {
		"/": {
			connection: {
				// URL will be injected by AppConfigService from environment variables
			},
			exchanges: {
				system_events_ex: { assert: true, type: "topic" },
				tenant_events_ex: { assert: true, type: "topic" },
				pbx_events_ex: { assert: true, type: "topic" },
				call_domain_events_ex: { assert: true, type: "topic" },
				schedule_domain_events_ex: { assert: true, type: "topic" }, // Already defined
				messaging_domain_events_ex: { assert: true, type: "topic" },
				chatbot_platform_events_ex: { assert: true, type: "topic" },
				chatbot_domain_events_ex: { assert: true, type: "topic" },
				dashboard_push_events_ex: { assert: true, type: "topic" },
				dead_letter_ex: { assert: true, type: "fanout" },
			},
			queues: {
				// ... other queues ...
				q_worker_pbx_event_processor: {
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" },
				},
				q_worker_call_command_processor: {
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" },
				},
				// ADD THIS QUEUE (or use an existing one if appropriate)
				q_worker_schedule_create_requested: {
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" },
				},
				q_worker_schedule_activation: { // This seems related, maybe rename or use one for both?
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" },
				},
				q_worker_sms_sender: {
					assert: true,
					options: {
						durable: true,
						deadLetterExchange: "dead_letter_ex",
						arguments: { "x-max-priority": 10 },
					},
				},
				q_worker_chatbot_telegram_ingest: {
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" },
				},
				q_worker_chatbot_domain_processor: {
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" },
				},
				q_websocket_push_service: {
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" },
				},
				q_dead_letter: {
					assert: true,
					options: { durable: true },
				},
			},
			bindings: {
				// ... other bindings ...
				bind_pbx_events_to_processor: {
					source: "pbx_events_ex",
					destination: "q_worker_pbx_event_processor",
					bindingKey: "pbx.#",
				},
				bind_call_commands_to_processor: {
					source: "call_domain_events_ex",
					destination: "q_worker_call_command_processor",
					bindingKey: "call.*.requested.#",
				},
				bind_call_domain_events_to_websocket_push: {
					source: "call_domain_events_ex",
					destination: "q_websocket_push_service",
					bindingKey: "call.*.created.#",
				},
				bind_call_domain_events_updated_to_websocket_push: {
					source: "call_domain_events_ex",
					destination: "q_websocket_push_service",
					bindingKey: "call.*.updated.#",
				},
				bind_sms_send_request_to_sender: {
					source: "messaging_domain_events_ex",
					destination: "q_worker_sms_sender",
					bindingKey: "sms.send.request.#",
				},
                // ADD THIS BINDING
                bind_schedule_create_requested_to_worker: {
                    source: "schedule_domain_events_ex", // Exchange where schedule events are published
                    destination: "q_worker_schedule_create_requested", // The new queue
                    bindingKey: "schedule.create.requested.#", // Match routing key from ScheduleCommandService
                },
				bind_dlx_to_dlq: {
					source: "dead_letter_ex",
					destination: "q_dead_letter",
                    // bindingKey: "#" // Fanout exchanges don't use binding keys typically, but if DLX is topic, add one
				},
			},
			publications: {
				// ... other publications ...
				pub_pbx_event: {
					exchange: "pbx_events_ex",
					routingKey: "pbx.${eventType}${tenantId ? '.' + tenantId : '.global'}",
				},
				pub_call_domain_event: {
					exchange: "call_domain_events_ex",
					routingKey: "${domain}.${action}.${tenantId}${entityId ? '.' + entityId : ''}",
				},
				// Ensure this publication is correctly defined and used by ScheduleCommandService
				pub_schedule_domain_event: { // Changed from pub_schedule_event to be more specific
					exchange: "schedule_domain_events_ex",
					routingKey: "schedule.${action}.${tenantId}${scheduleId ? '.' + scheduleId : ''}",
				},
				pub_messaging_domain_event: {
					exchange: "messaging_domain_events_ex",
					routingKey: "${entity}.${action}.${tenantId}",
				},
				pub_chatbot_platform_event: {
					exchange: "chatbot_platform_events_ex",
					routingKey: "chatbot.${platform}.${eventType}.${tenantId}.${botId}",
				},
				pub_chatbot_domain_event: {
					exchange: "chatbot_domain_events_ex",
					routingKey: "chatbot.domain.${action}.${tenantId}.${botId}${sessionId ? '.' + sessionId : ''}",
				},
				pub_dashboard_push_event: {
					exchange: "dashboard_push_events_ex",
					routingKey: "dashboard.push.${tenantId}${userId ? '.user.' + userId : '.all_operators'}.${dataType}",
				},
			},
			subscriptions: {
				// ... other subscriptions ...
				sub_pbx_event_processor: {
					queue: "q_worker_pbx_event_processor",
					contentType: "application/json",
					prefetch: 5,
				},
				sub_websocket_push_service: {
					queue: "q_websocket_push_service",
					contentType: "application/json",
					prefetch: 10,
				},
                // ADD THIS SUBSCRIPTION
                sub_schedule_create_requested: {
                    queue: "q_worker_schedule_create_requested", // Points to the new queue
                    contentType: "application/json",
                    prefetch: 1, // Adjust as needed
                },
			},
		},
	},
};