import { ConfigModule } from "@/config/config.module";
import { Global, Module } from "@nestjs/common";
import { RascalService } from "./rascal.service";
import { AppConfigService } from "@/config/app-config.service";
import { rascalDefinitions } from "@/config/rascal-definitions";
import { BrokerConfig } from "rascal";
import { RASCAL_CONFIG_TOKEN } from "./rascal.constants"; // Import the token

@Global()
@Module({
	imports: [
		ConfigModule, // Ensures AppConfigService is available
	],
	providers: [
		{
			provide: RASCAL_CONFIG_TOKEN, // Provide the BrokerConfig under this token
			useFactory: (appConfigService: AppConfigService): BrokerConfig => {
				const config: BrokerConfig = JSON.parse(
					JSON.stringify(rascalDefinitions),
				); // Deep clone
				if (config.vhosts?.["/"]?.connection) {
					config.vhosts["/"].connection.url = appConfigService.rabbitMQUrl;
				} else {
					// Handle case where structure might be missing, or log a warning
					console.warn("Rascal definitions missing expected vhost/connection structure for URL injection.");
					// Ensure a default or throw an error if critical
					config.vhosts = config.vhosts || {};
					config.vhosts["/"] = config.vhosts["/"] || {};
					config.vhosts["/"].connection = { url: appConfigService.rabbitMQUrl, ...config.vhosts["/"].connection };
				}
				return config;
			},
			inject: [AppConfigService],
		},
		{
			provide: RascalService,
			useFactory: (brokerConfig: BrokerConfig) => new RascalService(brokerConfig),
			inject: [RASCAL_CONFIG_TOKEN], // Inject the BrokerConfig
		},
	],
	exports: [RascalService],
})
export class RascalModule {}