import {
	Injectable,
	Logger,
	type OnModuleDestroy,
	type OnModuleInit,
} from "@nestjs/common";
import Rascal, { AckOrNack } from "rascal"; // Removed Message from here
import { Message } from "amqplib"; // Import Message directly from amqplib
import {
	BrokerAsPromised,
	type BrokerConfig,
	SubscriberSessionAsPromised,
} from "rascal";

// Export AckOrNack and define specific handler types for different Rascal events
export type MessageHandler = (
	message: Message,
	content: any,
	ackOrNack: AckOrNack,
) => void | Promise<void>;
export type ErrorHandler = (error: Error) => void;
export type CancelledHandler = (err?: Error) => void;
export type InvalidContentHandler = (err: Error, message: Message) => void;
export type RedeliveriesExceededHandler = (
	err: Error,
	message: Message,
) => void;
export type RedeliveriesErrorHandler = (err: Error, message: Message) => void;

// Define the structure for the eventHandlers parameter in subscribe
export interface SubscriptionEventHandlers {
	message?: MessageHandler;
	error?: ErrorHandler;
	cancelled?: CancelledHandler;
	invalid_content?: InvalidContentHandler;
	redeliveries_exceeded?: RedeliveriesExceededHandler;
	redeliveries_error?: RedeliveriesErrorHandler;
}

@Injectable()
export class RascalService implements OnModuleInit, OnModuleDestroy {
	private broker: BrokerAsPromised | null = null;
	private readonly logger = new Logger(RascalService.name);

	constructor(private readonly brokerConfig: BrokerConfig) {
		// Accept the full BrokerConfig
	}

	async onModuleInit() {
		try {
			this.broker = await Rascal.BrokerAsPromised.create(this.brokerConfig); // Use the injected config
			this.broker.on("vhost_initialised", ({ vhost, connectionUrl }) => {
				this.logger.log(
					`VHost: ${vhost} initialised with connection: ${connectionUrl}`,
				);
			});
			this.broker.on("vhost_failed", ({ vhost, connectionUrl, err }) => {
				this.logger.error(
					`VHost: ${vhost} failed to initialise with connection: ${connectionUrl}`,
					err.stack,
				);
			});
			this.broker.on("error", (err, { vhost, connectionUrl }) => {
				this.logger.error(
					`Rascal Broker error for VHost: ${vhost} (${connectionUrl})`,
					err.stack,
				);
			});
			this.broker.on("connected", ({ vhost, connectionUrl }) => {
				this.logger.log(
					`Successfully connected to RabbitMQ VHost: ${vhost} via ${connectionUrl}`,
				);
			});
			this.broker.on("disconnected", ({ vhost, connectionUrl }) => {
				this.logger.warn(
					`Disconnected from RabbitMQ VHost: ${vhost} via ${connectionUrl}. Attempting to reconnect...`,
				);
			});

			this.logger.log(
				"Rascal Broker initialized successfully and event listeners attached.",
			);
		} catch (error) {
			this.logger.error(
				"Failed to initialize RabbitMQ Broker",
				error instanceof Error ? error.stack : error,
			);
			throw error;
		}
	}

	async onModuleDestroy() {
		this.logger.log("Shutting down Rascal broker...");
		if (this.broker) {
			try {
				await this.broker.shutdown();
				this.logger.log("Rascal broker shut down successfully.");
			} catch (error) {
				this.logger.error(
					"Error during RabbitMQ broker shutdown",
					error instanceof Error ? error.stack : error,
				);
			}
		}
	}

	getRecoveryStrategy(error: any): "republish" | "nack" | "forward" {
		if (error.isRetriable) {
			return "republish";
		}
		return "nack";
	}

	public async publish(
		publicationName: string,
		message: any,
		options?: Record<string, any>,
	): Promise<string> {
		if (!this.broker) {
			this.logger.error(
				`Cannot publish to ${publicationName}: Broker not initialized.`,
			);
			throw new Error("Rascal broker is not initialized.");
		}
		try {
			const publication = await this.broker.publish(
				publicationName,
				message,
				options,
			);
			return new Promise((resolve, reject) => {
				publication.on("success", resolve);
				publication.on("error", reject);
			});
		} catch (error) {
			this.logger.error(
				`Failed to publish to ${publicationName}`,
				error instanceof Error ? error.stack : error,
			);
			throw error;
		}
	}

	public async subscribe(
		subscriptionName: string,
		eventHandlers: SubscriptionEventHandlers, // Use the new, more specific type
		options?: Record<string, any>,
	): Promise<SubscriberSessionAsPromised> {
		if (!this.broker) {
			this.logger.error(
				`Cannot subscribe to ${subscriptionName}: Broker not initialized.`,
			);
			throw new Error("Rascal broker is not initialized.");
		}
		const subscription = await this.broker.subscribe(subscriptionName, options);

		// Handle specific event types with proper type checking
		if (eventHandlers.message) {
			subscription.on("message", eventHandlers.message);
		}
		if (eventHandlers.error) {
			subscription.on("error", eventHandlers.error);
		}
		if (eventHandlers.cancelled) {
			subscription.on("cancelled", eventHandlers.cancelled);
		}
		if (eventHandlers.invalid_content) {
			subscription.on("invalid_content", eventHandlers.invalid_content);
		}
		if (eventHandlers.redeliveries_exceeded) {
			subscription.on(
				"redeliveries_exceeded",
				eventHandlers.redeliveries_exceeded,
			);
		}
		if (eventHandlers.redeliveries_error) {
			subscription.on("redeliveries_error", eventHandlers.redeliveries_error);
		}

		return subscription;
	}
}
