import { Injectable, type CanActivate, type ExecutionContext, UnauthorizedException } from '@nestjs/common';
import type { ConfigService } from '@nestjs/config'; // Use NestJS ConfigService

@Injectable()
export class DevApiKeyGuard implements CanActivate {
  private readonly devApiKey: string;
 
  constructor(private readonly configService: ConfigService) {
    this.devApiKey = this.configService.get<string>('DEV_ADMIN_API_KEY') as string;
    if (!this.devApiKey) {
      console.warn('DEV_ADMIN_API_KEY is not set. Admin endpoints will be inaccessible with DevApiKeyGuard.');
    }
  }

  canActivate(context: ExecutionContext): boolean {
    if (process.env.NODE_ENV === 'production' && this.devApiKey) {
      // Optional: prevent this guard from working in production even if key is set
      console.error("DevApiKeyGuard should not be used in production!");
      throw new UnauthorizedException('Internal Server Configuration Error');
    }
    if (!this.devApiKey) { // If key is not set at all, block access
        throw new UnauthorizedException('Admin API Key not configured for development.');
    }

    const request = context.switchToHttp().getRequest();
    const apiKey = request.headers['x-dev-admin-key'];
    if (apiKey && apiKey === this.devApiKey) {
      return true;
    }
    throw new UnauthorizedException('Missing or invalid Developer API Key for Admin access.');
  }
}