// biome-ignore lint/style/useImportType: <explanation>
import { AppConfigService } from "@/config/app-config.service.js";
import { Inject, Injectable, type OnModuleInit } from "@nestjs/common";
import type { IRedisService } from "@/redis/redis.interface.js";
import { RedisServiceToken } from "@/redis/redis.token.js";
import { Verrou } from "@verrou/core";
import { memoryStore } from "@verrou/core/drivers/memory";
import { redisStore } from "@verrou/core/drivers/redis";

@Injectable()
export class LocksService implements OnModuleInit {
	public verrou!: Verrou<{
		memory: { driver: ReturnType<typeof memoryStore> };
		redis: { driver: ReturnType<typeof redisStore> };
	}>; // Make instance accessible

	constructor(
		private readonly config: AppConfigService,
		@Inject(RedisServiceToken)
		private readonly redisDataService: IRedisService, // Inject IRedisService (implemented by RedisDataService)
	) {}

	async onModuleInit() {
		const { Verrou } = await import("@verrou/core");
		const { memoryStore } = await import("@verrou/core/drivers/memory");
		const { redisStore } = await import("@verrou/core/drivers/redis");

		this.verrou = new Verrou({
			default: this.config.lockStore === "redis" ? "redis" : "memory",
			stores: {
				memory: { driver: memoryStore() },
				redis: {
					driver: redisStore({ connection: this.redisDataService.getCacheClient() }), // Use client from RedisDataService
				},
			},
		});
	}
}
