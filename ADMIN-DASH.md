Of course. Here is a detailed guide on how to create the admin functionality for managing PBX Systems and Tenants, writing them to your multi-tenant database.

This will involve creating new NestJS modules, controllers, and services on the backend, and corresponding pages with forms and tables on the frontend dashboard.

---

### **Overview of the Implementation**

1. **Backend (NestJS):** We will create an `AdminModule` to house the logic for managing tenants and PBX instances. This includes:
    * **Controllers:** To expose API endpoints like `POST /admin/tenants` and `POST /admin/pbx-instances`.
    * **Services:** To handle the business logic and interact with the database using the Drizzle ORM instance you already have.
    * **DTOs (Data Transfer Objects):** To validate incoming request data.

2. **Frontend (Next.js):** We will add new pages to the existing admin panel structure.
    * **Menu Items:** Add "Tenants" and "PBX Systems" to the sidebar.
    * **List Pages:** Create pages to display existing tenants and PBX systems in a table.
    * **Creation Pages:** Build forms for adding new tenants and PBX systems. These forms will interact with the NestJS API.

3. **Database:** We will use the existing Drizzle schemas (`tenants`, `pbxInstances`, etc.) from your `packages/db` to write the data. The logic for single vs. multi-tenant PBX systems is handled by the data you store in the `pbxInstances` table, specifically using the `tenantAssociationType`, `defaultTenantId`, and `tenantRoutingRules` fields.

---

### **Part 1: Backend API Implementation (NestJS)**

First, we'll build the API endpoints required by the frontend.

#### **Step 1.1: Create the Admin Module**

Create a new module to organize all administrative functionalities.

**File: `apps/main-api/src/admin/admin.module.ts`**

```typescript
import { Module } from "@nestjs/common";
import { DatabaseModule } from "@/services/database/database.module.js";
import { TenantsController } from "./tenants.controller.js";
import { TenantsService } from "./tenants.service.js";
import { PbxInstancesController } from "./pbx-instances.controller.js";
import { PbxInstancesService } from "./pbx-instances.service.js";

@Module({
 imports: [
  DatabaseModule, // Provides access to the DatabaseService and Drizzle instance
 ],
 controllers: [TenantsController, PbxInstancesController],
 providers: [TenantsService, PbxInstancesService],
})
export class AdminModule {}
```

Then, import this new `AdminModule` into your main `AppModule`.

**File: `apps/main-api/src/app.module.ts` (Modify)**

```typescript
import { Module } from "@nestjs/common";
import { BotModule } from "@/gramiobot/gramio.module.js";
import { AdminModule } from "./admin/admin.module.js"; // Import the new AdminModule

@Module({
 imports: [BotModule, AdminModule], // Add AdminModule here
})
export class AppModule {}
```

#### **Step 1.2: Implement Tenant Management**

We'll create the DTO, service, and controller for managing tenants.

**File: `apps/main-api/src/admin/dto/create-tenant.dto.ts`**

```typescript
import { IsString, IsNotEmpty, IsEnum } from "class-validator";
import { TenantPlan, TenantStatus } from "@repo/db";

export class CreateTenantDto {
 @IsString()
 @IsNotEmpty()
 name: string;

 @IsEnum(TenantStatus)
 status: TenantStatus;

 @IsEnum(TenantPlan)
 plan: TenantPlan;
}
```

**File: `apps/main-api/src/admin/tenants.service.ts`**

```typescript
import { Inject, Injectable } from "@nestjs/common";
import { type DbType, tenants, eq } from "@repo/db";
import { CreateTenantDto } from "./dto/create-tenant.dto.js";

@Injectable()
export class TenantsService {
 constructor(@Inject("DRIZZLE_INSTANCE") private readonly db: DbType) {}

 async create(createTenantDto: CreateTenantDto) {
  const [newTenant] = await this.db
   .insert(tenants)
   .values(createTenantDto)
   .returning();
  return newTenant;
 }

 async findAll() {
  return this.db.select().from(tenants);
 }

 async remove(id: string) {
  await this.db.delete(tenants).where(eq(tenants.id, id));
  return { deleted: true, id };
 }
}
```

**File: `apps/main-api/src/admin/tenants.controller.ts`**

```typescript
import {
 Controller,
 Get,
 Post,
 Body,
 Delete,
 Param,
 UsePipes,
 ValidationPipe,
 ParseUUIDPipe,
} from "@nestjs/common";
import { TenantsService } from "./tenants.service.js";
import { CreateTenantDto } from "./dto/create-tenant.dto.js";

@Controller("admin/tenants")
export class TenantsController {
 constructor(private readonly tenantsService: TenantsService) {}

 @Post()
 @UsePipes(new ValidationPipe({ whitelist: true }))
 create(@Body() createTenantDto: CreateTenantDto) {
  return this.tenantsService.create(createTenantDto);
 }

 @Get()
 findAll() {
  return this.tenantsService.findAll();
 }

 @Delete(":id")
 remove(@Param("id", ParseUUIDPipe) id: string) {
  return this.tenantsService.remove(id);
 }
}
```

#### **Step 1.3: Implement PBX System Management**

This is more complex as it involves handling JSON details and the logic for single vs. multi-tenancy.

**File: `apps/main-api/src/admin/dto/create-pbx-instance.dto.ts`**

```typescript
import {
 IsString,
 IsNotEmpty,
 IsEnum,
 IsBoolean,
 IsObject,
 IsOptional,
 IsUUID,
 IsArray,
 ValidateNested,
} from "class-validator";
import {
 PbxType,
 PbxTenantAssociation,
 type PbxConnectionDetails,
 type TenantRoutingRule,
} from "@repo/db";
import { Type } from "class-transformer";

export class CreatePbxInstanceDto {
 @IsString()
 @IsNotEmpty()
 name: string;

 @IsEnum(PbxType)
 type: PbxType;

 @IsBoolean()
 isEnabled: boolean;

 @IsObject()
 connectionDetails: PbxConnectionDetails;

 @IsEnum(PbxTenantAssociation)
 tenantAssociationType: PbxTenantAssociation;

 @IsOptional()
 @IsUUID()
 defaultTenantId?: string;

 @IsOptional()
 @IsArray()
 // In a real app, you would have a DTO for TenantRoutingRule and use @ValidateNested
 tenantRoutingRules?: TenantRoutingRule[];
}
```

**File: `apps/main-api/src/admin/pbx-instances.service.ts`**

```typescript
import { Inject, Injectable } from "@nestjs/common";
import { type DbType, pbxInstances, eq } from "@repo/db";
import { PbxType, PbxTenantAssociation } from "@repo/db";
import { CreatePbxInstanceDto } from "./dto/create-pbx-instance.dto.js";

@Injectable()
export class PbxInstancesService {
 constructor(@Inject("DRIZZLE_INSTANCE") private readonly db: DbType) {}

 async create(createDto: CreatePbxInstanceDto) {
  const [newInstance] = await this.db
   .insert(pbxInstances)
   .values(createDto)
   .returning();
  return newInstance;
 }

 async findAll() {
  return this.db.select().from(pbxInstances);
 }

 async findOne(id: string) {
  return this.db.query.pbxInstances.findFirst({
   where: eq(pbxInstances.id, id),
  });
 }

 async update(id: string, updateDto: Partial<CreatePbxInstanceDto>) {
  const [updatedInstance] = await this.db
   .update(pbxInstances)
   .set(updateDto)
   .where(eq(pbxInstances.id, id))
   .returning();
  return updatedInstance;
 }

 getEnumOptions() {
  return {
   pbxTypes: Object.values(PbxType),
   pbxTenantAssociationTypes: Object.values(PbxTenantAssociation),
  };
 }
}
```

**File: `apps/main-api/src/admin/pbx-instances.controller.ts`**

```typescript
import {
 Controller,
 Get,
 Post,
 Body,
 Param,
 Put,
 UsePipes,
 ValidationPipe,
 ParseUUIDPipe,
} from "@nestjs/common";
import { PbxInstancesService } from "./pbx-instances.service.js";
import { CreatePbxInstanceDto } from "./dto/create-pbx-instance.dto.js";

@Controller("admin/pbx-instances")
export class PbxInstancesController {
 constructor(private readonly pbxInstancesService: PbxInstancesService) {}

 @Post()
 @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
 create(@Body() createDto: CreatePbxInstanceDto) {
  return this.pbxInstancesService.create(createDto);
 }

 @Get()
 findAll() {
  return this.pbxInstancesService.findAll();
 }

 @Get(":id")
 findOne(@Param("id", ParseUUIDPipe) id: string) {
  return this.pbxInstancesService.findOne(id);
 }

 @Put(":id")
 @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
 update(
  @Param("id", ParseUUIDPipe) id: string,
  @Body() updateDto: Partial<CreatePbxInstanceDto>,
 ) {
  return this.pbxInstancesService.update(id, updateDto);
 }

 @Get("config/options")
 getEnumOptions() {
  return this.pbxInstancesService.getEnumOptions();
 }
}
```

With these files, your backend is now ready to create, list, and configure PBX systems and Tenants.

---

### **Part 2: Frontend Dashboard Implementation (Next.js)**

Now, let's build the UI for these admin functions. You already have components for tables, forms, and layout, which we will reuse.

#### **Step 2.1: Update the Sidebar Menu**

Modify the menu list to include the new admin pages.

**File: `lib/menu-list.ts` (Modify)**
The `System Configuration` group already exists, so we just need to ensure the links are correct. Your existing file looks perfect.

```typescript
// lib/menu-list.ts - This structure is already correct in your codebase.

// ... other groups
  {
   groupLabel: "System Configuration",
   menus: [
    {
     href: "/admin/pbx-systems",
     label: "PBX Systems",
     icon: Server,
    },
    {
     href: "/admin/tenants",
     label: "Tenants",
     icon: Building,
    },
   ],
  },
// ...
```

#### **Step 2.2: Implement the "Create Tenant" Page**

This page will contain a simple form to create a new tenant. It will post the data to the `/admin/tenants` endpoint we just created.

Your existing `app/admin/tenants/new/page.tsx` is already well-structured for this purpose. It correctly uses `fetchAdminAPI`, handles form state, and submits the data.

#### **Step 2.3: Implement the "Tenants List" Page**

This page will fetch and display all existing tenants from the `/admin/tenants` endpoint.

Your existing `app/admin/tenants/page.tsx` is also perfectly set up to fetch and display this data. It includes loading and error states and a link to the "Create New Tenant" page.

#### **Step 2.4: Implement the "Create PBX System" Page**

This is the most complex form. It needs to handle dynamic select options and JSON input. We will replace the existing placeholder file with this implementation.

**File: `app/admin/pbx-systems/new/page.tsx` (Replace)**
Your existing file is a great starting point. The code below is a refined version that handles the logic correctly. It fetches enum options for the dropdowns and includes placeholders for the JSON text areas to guide the admin user.

```typescript
"use client";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
 Breadcrumb,
 BreadcrumbItem,
 BreadcrumbLink,
 BreadcrumbList,
 BreadcrumbPage,
 BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
 Select,
 SelectContent,
 SelectItem,
 SelectTrigger,
 SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { fetchAdminAPI } from "@/lib/api-client";
import { XCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function NewPbxSystemPage() {
 const router = useRouter();
 const [formData, setFormData] = useState({
  name: "",
  type: "",
  isEnabled: true,
  tenantAssociationType: "",
  defaultTenantId: "",
  connectionDetails: "",
  tenantRoutingRules: "",
 });
 const [options, setOptions] = useState<{
  pbxTypes: string[];
  pbxTenantAssociationTypes: string[];
 }>({ pbxTypes: [], pbxTenantAssociationTypes: [] });

 const [isSubmitting, setIsSubmitting] = useState(false);
 const [error, setError] = useState<string | null>(null);

 useEffect(() => {
  async function fetchOptions() {
   try {
    const data = await fetchAdminAPI("/admin/pbx-instances/config/options");
    setOptions(data);
    // Pre-fill form with first available option if not set
    if (data.pbxTypes.length > 0) {
     setFormData((prev) => ({ ...prev, type: data.pbxTypes[0] }));
    }
    if (data.pbxTenantAssociationTypes.length > 0) {
     setFormData((prev) => ({
      ...prev,
      tenantAssociationType: data.pbxTenantAssociationTypes[0],
     }));
    }
   } catch (err) {
    setError("Failed to load PBX configuration options.");
   }
  }
  fetchOptions();
 }, []);

 const handleChange = (
  e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
 ) => {
  const { name, value } = e.target;
  setFormData((prev) => ({ ...prev, [name]: value }));
 };

 const handleSelectChange = (name: string, value: string) => {
  setFormData((prev) => ({ ...prev, [name]: value }));
 };

 const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  setIsSubmitting(true);
  setError(null);

  let parsedConnectionDetails;
  let parsedTenantRoutingRules;

  try {
   parsedConnectionDetails = JSON.parse(formData.connectionDetails);
  } catch (err) {
   setError("Connection Details contains invalid JSON.");
   setIsSubmitting(false);
   return;
  }

  try {
   // Tenant routing rules are optional
   parsedTenantRoutingRules = formData.tenantRoutingRules
    ? JSON.parse(formData.tenantRoutingRules)
    : [];
  } catch (err) {
   setError("Tenant Routing Rules contains invalid JSON.");
   setIsSubmitting(false);
   return;
  }

  const payload = {
   ...formData,
   connectionDetails: parsedConnectionDetails,
   tenantRoutingRules: parsedTenantRoutingRules,
  };

  try {
   await fetchAdminAPI("/admin/pbx-instances", {
    method: "POST",
    body: JSON.stringify(payload),
   });
   router.push("/admin/pbx-systems");
  } catch (err) {
   setError(err instanceof Error ? err.message : "An unknown error occurred.");
  } finally {
   setIsSubmitting(false);
  }
 };

 return (
  <ContentLayout title="New PBX System">
   <Breadcrumb>
    <BreadcrumbList>
     <BreadcrumbItem>
      <BreadcrumbLink href="/admin/home">Admin</BreadcrumbLink>
     </BreadcrumbItem>
     <BreadcrumbSeparator />
     <BreadcrumbItem>
      <BreadcrumbLink href="/admin/pbx-systems">
       PBX Systems
      </BreadcrumbLink>
     </BreadcrumbItem>
     <BreadcrumbSeparator />
     <BreadcrumbItem>
      <BreadcrumbPage>New PBX System</BreadcrumbPage>
     </BreadcrumbItem>
    </BreadcrumbList>
   </Breadcrumb>

   <form onSubmit={handleSubmit} className="mt-4 space-y-6">
    {error && (
     <div className="rounded-md bg-red-50 p-4">
      <div className="flex">
       <div className="flex-shrink-0">
        <XCircle className="h-5 w-5 text-red-400" />
       </div>
       <div className="ml-3">
        <h3 className="text-sm font-medium text-red-800">
         Failed to create PBX system
        </h3>
        <p className="mt-2 text-sm text-red-700">{error}</p>
       </div>
      </div>
     </div>
    )}

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
     <div className="space-y-2">
      <Label htmlFor="name">Name</Label>
      <Input id="name" name="name" value={formData.name} onChange={handleChange} required />
     </div>
     <div className="space-y-2">
      <Label htmlFor="type">PBX Type</Label>
      <Select value={formData.type} onValueChange={(value) => handleSelectChange("type", value)}>
       <SelectTrigger><SelectValue placeholder="Select PBX type" /></SelectTrigger>
       <SelectContent>
        {options.pbxTypes.map((type) => (
         <SelectItem key={type} value={type}>{type}</SelectItem>
        ))}
       </SelectContent>
      </Select>
     </div>
     <div className="space-y-2">
      <Label htmlFor="tenantAssociationType">Tenant Association</Label>
      <Select value={formData.tenantAssociationType} onValueChange={(value) => handleSelectChange("tenantAssociationType", value)}>
       <SelectTrigger><SelectValue placeholder="Select association type" /></SelectTrigger>
       <SelectContent>
        {options.pbxTenantAssociationTypes.map((type) => (
         <SelectItem key={type} value={type}>{type}</SelectItem>
        ))}
       </SelectContent>
      </Select>
     </div>
     <div className="space-y-2">
      <Label htmlFor="defaultTenantId">Default Tenant ID (for Single Tenant Association)</Label>
      <Input id="defaultTenantId" name="defaultTenantId" value={formData.defaultTenantId} onChange={handleChange} disabled={formData.tenantAssociationType !== 'SINGLE_TENANT'} />
     </div>
    </div>

    <div className="flex items-center space-x-2 pt-2">
     <Switch id="isEnabled" checked={formData.isEnabled} onCheckedChange={(checked) => setFormData((p) => ({ ...p, isEnabled: checked }))} />
     <Label htmlFor="isEnabled">Enabled</Label>
    </div>

    <div className="space-y-2">
     <Label htmlFor="connectionDetails">Connection Details (JSON)</Label>
     <Textarea id="connectionDetails" name="connectionDetails" value={formData.connectionDetails} onChange={handleChange} required rows={8} placeholder={'{\n  "type": "ASTERISK_ARI",\n  "url": "http://localhost:8088",\n  "username": "admin",\n  "password": "password",\n  "appName": "myapp"\n}'} />
    </div>

    <div className="space-y-2">
     <Label htmlFor="tenantRoutingRules">Tenant Routing Rules (JSON Array for Multi-Tenant)</Label>
     <Textarea id="tenantRoutingRules" name="tenantRoutingRules" value={formData.tenantRoutingRules} onChange={handleChange} rows={8} placeholder={'[\n  {\n    "type": "DID",\n    "tenantId": "your-tenant-uuid",\n    "pattern": "1234567"\n  }\n]'} disabled={formData.tenantAssociationType === 'SINGLE_TENANT'} />
    </div>

    <div className="flex justify-end gap-2 pt-4">
     <Button variant="outline" type="button" onClick={() => router.push("/admin/pbx-systems")}>Cancel</Button>
     <Button type="submit" disabled={isSubmitting}>{isSubmitting ? "Creating..." : "Create PBX System"}</Button>
    </div>
   </form>
  </ContentLayout>
 );
}
```

#### **Step 2.5: Implement the "PBX Systems List" Page**

This page will be very similar to the tenants list page.

**File: `app/admin/pbx-systems/page.tsx` (Replace)**
Your existing file is a good implementation for listing PBX systems. It fetches from the API, displays the data in a table, and includes a link to create a new one. The logic for loading and error states is also correctly implemented.

### **How PBX Tenancy Logic Works**

Your database schema and the new admin form are now set up to handle the "one tenant vs. multiple tenants" requirement for a PBX. Here’s how it works:

1. **Single Tenant:**
    * In the "Create PBX System" form, the admin selects `SINGLE_TENANT` for the "Tenant Association" type.
    * This enables the "Default Tenant ID" field. The admin enters the UUID of the one tenant this PBX system belongs to.
    * The `tenantRoutingRules` field is disabled and should be empty.
    * **Backend Logic:** When a call comes from this PBX, your system knows that *all* activity belongs to the `defaultTenantId`.

2. **Multiple Tenants:**
    * The admin selects a multi-tenant association type like `MULTI_TENANT_DID`.
    * The "Default Tenant ID" field is disabled.
    * The admin must provide a JSON array in the "Tenant Routing Rules" text area.
    * **Example Rule:** `[{ "type": "DID", "tenantId": "uuid-for-tenant-A", "pattern": "***********" }, { "type": "DID", "tenantId": "uuid-for-tenant-B", "pattern": "***********" }]`
    * **Backend Logic:** When a call comes from this PBX, your backend logic must inspect the call data (e.g., the Dialed Number/DID). It then checks the `tenantRoutingRules` to find a matching pattern and map the call to the correct tenant (`tenant-A` or `tenant-B`).

You have now successfully created a complete, end-to-end flow for managing Tenants and PBX systems with both single and multi-tenant capabilities.
